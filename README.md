# نظام إدارة تجربة العملاء

نظام شامل لإدارة تجربة العملاء يتضمن جميع الميزات المطلوبة لتتبع وتحسين رضا العملاء.

## الميزات الرئيسية

### 1. إدارة العملاء الأساسية
- إضافة وتعديل وحذف العملاء
- عرض قائمة العملاء مع معلوماتهم الأساسية
- البحث والفلترة

### 2. نظام تقييم العملاء ⭐
- تقييم العملاء بنظام النجوم (1-5)
- تصنيف التقييمات حسب الفئة (عام، خدمة، منتج، دعم فني)
- إضافة تعليقات مع التقييمات
- حساب متوسط التقييم لكل عميل وللنظام ككل

### 3. نظام ملاحظات العملاء 📝
- إضافة ملاحظات مفصلة لكل عميل
- تصنيف الملاحظات (عامة، مهمة، متابعة، تذكير)
- تحديد أولوية الملاحظات (منخفضة، متوسطة، عالية)
- تتبع كاتب الملاحظة وتاريخ الإنشاء

### 4. نظام تتبع تفاعلات العملاء 💬
- تسجيل جميع أنواع التفاعلات:
  - المكالمات الهاتفية
  - الاجتماعات
  - البريد الإلكتروني
  - المحادثات
  - الزيارات
- تتبع مدة التفاعل ونتيجته
- تحديد الإجراءات التالية المطلوبة
- إحصائيات شاملة للتفاعلات

### 5. نظام الشكاوى والاقتراحات 🎯
- إدارة شاملة للشكاوى والاقتراحات
- أنواع مختلفة: شكوى، اقتراح، إطراء، استفسار
- نظام أولويات متقدم (منخفض، متوسط، عالي، عاجل)
- تتبع حالة المعالجة (مفتوح، قيد المعالجة، محلول، مغلق)
- تعيين مسؤولين للمتابعة
- توثيق الحلول والقرارات

### 6. لوحة تحكم تجربة العملاء 📊
- إحصائيات شاملة ومؤشرات الأداء الرئيسية
- رسوم بيانية تفاعلية لتوزيع التفاعلات والشكاوى
- عرض النشاطات الحديثة
- تنبيهات للشكاوى العاجلة والمتابعات المطلوبة

### 7. نظام التقارير المتقدم 📈
- تقارير شاملة لتجربة العملاء
- تحليل التقييمات حسب الفئات
- إحصائيات التفاعلات والشكاوى
- قائمة العملاء الأعلى تقييماً
- تتبع النشاط الشهري
- إمكانية طباعة التقارير

## التقنيات المستخدمة

- **Backend**: PHP 7.4+
- **Database**: MySQL
- **Frontend**: HTML5, CSS3, JavaScript
- **Framework CSS**: Bootstrap 5.1.3
- **Icons**: Font Awesome 6.0
- **Charts**: Chart.js
- **Architecture**: MVC Pattern

## هيكل المشروع

```
feedback/
├── actions/                 # إجراءات معالجة البيانات
│   ├── add_customer.php
│   ├── add_rating.php
│   ├── add_note.php
│   ├── add_interaction.php
│   ├── add_feedback.php
│   └── delete_customer.php
├── database/               # قاعدة البيانات
│   ├── connection.php
│   └── schema.sql
├── models/                 # نماذج البيانات
│   ├── Customer.php
│   ├── CustomerRating.php
│   ├── CustomerNote.php
│   ├── CustomerInteraction.php
│   └── CustomerFeedback.php
├── index.php              # الصفحة الرئيسية
├── customer_profile.php   # ملف العميل الشخصي
├── dashboard.php          # لوحة التحكم
├── reports.php           # التقارير
└── README.md             # هذا الملف
```

## التثبيت والإعداد

### 1. متطلبات النظام
- PHP 7.4 أو أحدث
- MySQL 5.7 أو أحدث
- خادم ويب (Apache/Nginx)

### 2. خطوات التثبيت

1. **نسخ الملفات**
   ```bash
   git clone [repository-url]
   cd feedback
   ```

2. **إعداد قاعدة البيانات**
   - إنشاء قاعدة بيانات جديدة
   - تشغيل ملف `database/schema.sql`
   - تحديث إعدادات الاتصال في `database/connection.php`

3. **إعداد الخادم**
   - رفع الملفات إلى خادم الويب
   - التأكد من صلاحيات الكتابة للمجلدات المطلوبة

### 3. إعداد قاعدة البيانات

```sql
-- تشغيل الأوامر التالية في MySQL
SOURCE database/schema.sql;
```

## الاستخدام

### 1. الصفحة الرئيسية
- عرض قائمة العملاء مع إحصائيات سريعة
- إضافة عملاء جدد
- الوصول السريع للوحة التحكم والتقارير

### 2. ملف العميل الشخصي
- عرض معلومات العميل الكاملة
- إدارة التقييمات والملاحظات
- تتبع التفاعلات والشكاوى
- واجهة تبويبات منظمة

### 3. لوحة التحكم
- مراقبة مؤشرات الأداء الرئيسية
- رسوم بيانية تفاعلية
- تنبيهات للمتابعات المطلوبة

### 4. التقارير
- تقارير شاملة قابلة للطباعة
- تحليلات متقدمة للبيانات
- إحصائيات مفصلة

## المساهمة

نرحب بالمساهمات لتحسين النظام:

1. Fork المشروع
2. إنشاء branch جديد للميزة
3. Commit التغييرات
4. Push إلى Branch
5. إنشاء Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف LICENSE للتفاصيل.

## الدعم

للحصول على الدعم أو الإبلاغ عن مشاكل، يرجى إنشاء Issue في المستودع.

---

**تم تطوير هذا النظام لتوفير حل شامل لإدارة تجربة العملاء بطريقة احترافية ومنظمة.**
