<?php
require_once __DIR__ . '/../models/RatingCategory.php';

if ($_POST && isset($_POST['name'])) {
    $ratingCategory = new RatingCategory();
    
    $name = trim($_POST['name']);
    $description = trim($_POST['description'] ?? '');
    $sort_order = isset($_POST['sort_order']) ? intval($_POST['sort_order']) : 0;
    
    if (!empty($name)) {
        // التحقق من عدم تكرار الاسم
        if ($ratingCategory->nameExists($name)) {
            header('Location: ../manage_categories.php?error=name_exists');
            exit;
        }
        
        $result = $ratingCategory->create($name, $description, $sort_order);
        
        if ($result) {
            header('Location: ../manage_categories.php?success=category_added');
        } else {
            header('Location: ../manage_categories.php?error=add_failed');
        }
    } else {
        header('Location: ../manage_categories.php?error=invalid_data');
    }
} else {
    header('Location: ../manage_categories.php?error=missing_data');
}
?>
