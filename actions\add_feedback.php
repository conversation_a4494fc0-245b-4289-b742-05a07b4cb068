<?php
require_once __DIR__ . '/../models/CustomerFeedback.php';

if ($_POST) {
    $feedback = new CustomerFeedback();
    $result = $feedback->create(
        $_POST['customer_id'],
        $_POST['feedback_type'],
        $_POST['title'],
        $_POST['description'],
        $_POST['priority'] ?? 'medium',
        $_POST['assigned_to'] ?? null
    );
    
    if ($result) {
        header('Location: ../customer_profile.php?id=' . $_POST['customer_id'] . '&success=feedback_added');
    } else {
        header('Location: ../customer_profile.php?id=' . $_POST['customer_id'] . '&error=feedback_failed');
    }
} else {
    header('Location: ../index.php');
}
?>
