<?php
require_once '../models/CustomerInteraction.php';

if ($_POST) {
    $interaction = new CustomerInteraction();
    $result = $interaction->create(
        $_POST['customer_id'],
        $_POST['interaction_type'],
        $_POST['subject'],
        $_POST['description'] ?? '',
        $_POST['duration_minutes'] ?? 0,
        $_POST['outcome'] ?? 'successful',
        $_POST['next_action'] ?? '',
        $_POST['interaction_date'] ?? null,
        $_POST['created_by'] ?? 'system'
    );
    
    if ($result) {
        header('Location: ../customer_profile.php?id=' . $_POST['customer_id'] . '&success=interaction_added');
    } else {
        header('Location: ../customer_profile.php?id=' . $_POST['customer_id'] . '&error=interaction_failed');
    }
} else {
    header('Location: ../index.php');
}
?>
