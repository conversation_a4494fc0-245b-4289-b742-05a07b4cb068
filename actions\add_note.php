<?php
require_once __DIR__ . '/../models/CustomerNote.php';

if ($_POST) {
    $note = new CustomerNote();
    $result = $note->create(
        $_POST['customer_id'],
        $_POST['note'],
        $_POST['note_type'] ?? 'general',
        $_POST['priority'] ?? 'medium',
        $_POST['created_by'] ?? 'system'
    );
    
    if ($result) {
        header('Location: ../customer_profile.php?id=' . $_POST['customer_id'] . '&success=note_added');
    } else {
        header('Location: ../customer_profile.php?id=' . $_POST['customer_id'] . '&error=note_failed');
    }
} else {
    header('Location: ../index.php');
}
?>
