<?php
require_once __DIR__ . '/../models/SurveyQuestion.php';

if ($_POST && isset($_POST['question_text']) && isset($_POST['question_type'])) {
    $surveyQuestion = new SurveyQuestion();
    
    $question_text = trim($_POST['question_text']);
    $question_type = $_POST['question_type'];
    $sort_order = isset($_POST['sort_order']) ? intval($_POST['sort_order']) : 0;
    
    if (!empty($question_text) && in_array($question_type, ['rating', 'text'])) {
        $result = $surveyQuestion->create($question_text, $question_type, $sort_order);
        
        if ($result) {
            header('Location: ../manage_questions.php?success=question_added');
        } else {
            header('Location: ../manage_questions.php?error=failed_to_add');
        }
    } else {
        header('Location: ../manage_questions.php?error=invalid_data');
    }
} else {
    header('Location: ../manage_questions.php?error=missing_data');
}
?>
