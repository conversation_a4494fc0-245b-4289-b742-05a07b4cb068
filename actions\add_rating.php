<?php
require_once '../models/CustomerRating.php';

if ($_POST) {
    $rating = new CustomerRating();
    $result = $rating->create(
        $_POST['customer_id'],
        $_POST['rating'],
        $_POST['comment'] ?? '',
        $_POST['category'] ?? 'general'
    );
    
    if ($result) {
        header('Location: ../customer_profile.php?id=' . $_POST['customer_id'] . '&success=rating_added');
    } else {
        header('Location: ../customer_profile.php?id=' . $_POST['customer_id'] . '&error=rating_failed');
    }
} else {
    header('Location: ../index.php');
}
?>
