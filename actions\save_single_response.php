<?php
header('Content-Type: application/json');

require_once __DIR__ . '/../models/CustomerSurveyResponse.php';

if ($_POST && isset($_POST['customer_id']) && isset($_POST['question_id'])) {
    $customer_id = intval($_POST['customer_id']);
    $question_id = intval($_POST['question_id']);
    $surveyResponse = new CustomerSurveyResponse();
    
    $rating_value = null;
    $text_value = null;
    
    if (isset($_POST['rating_value'])) {
        $rating_value = intval($_POST['rating_value']);
        if ($rating_value < 1 || $rating_value > 5) {
            echo json_encode(['success' => false, 'error' => 'Invalid rating value']);
            exit;
        }
    }
    
    if (isset($_POST['text_value'])) {
        $text_value = trim($_POST['text_value']);
        if (empty($text_value)) {
            $text_value = null;
        }
    }
    
    if ($rating_value !== null || $text_value !== null) {
        $result = $surveyResponse->saveResponse($customer_id, $question_id, $rating_value, $text_value);
        
        if ($result) {
            echo json_encode(['success' => true, 'message' => 'Response saved successfully']);
        } else {
            echo json_encode(['success' => false, 'error' => 'Failed to save response']);
        }
    } else {
        echo json_encode(['success' => false, 'error' => 'No valid response data']);
    }
} else {
    echo json_encode(['success' => false, 'error' => 'Missing required parameters']);
}
?>
