<?php
require_once __DIR__ . '/../models/CustomerSurveyResponse.php';
require_once __DIR__ . '/../models/SurveyQuestion.php';

if ($_POST && isset($_POST['customer_id'])) {
    $customer_id = $_POST['customer_id'];
    $surveyResponse = new CustomerSurveyResponse();
    $surveyQuestion = new SurveyQuestion();
    
    $questions = $surveyQuestion->getAllActive();
    $success_count = 0;
    $total_responses = 0;
    
    foreach ($questions as $question) {
        $question_id = $question['id'];
        
        if ($question['question_type'] == 'rating') {
            $rating_key = 'rating_' . $question_id;
            if (isset($_POST[$rating_key]) && !empty($_POST[$rating_key])) {
                $rating_value = intval($_POST[$rating_key]);
                if ($rating_value >= 1 && $rating_value <= 5) {
                    if ($surveyResponse->saveResponse($customer_id, $question_id, $rating_value, null)) {
                        $success_count++;
                    }
                    $total_responses++;
                }
            }
        } elseif ($question['question_type'] == 'text') {
            $text_key = 'text_' . $question_id;
            if (isset($_POST[$text_key]) && !empty(trim($_POST[$text_key]))) {
                $text_value = trim($_POST[$text_key]);
                if ($surveyResponse->saveResponse($customer_id, $question_id, null, $text_value)) {
                    $success_count++;
                }
                $total_responses++;
            }
        }
    }
    
    if ($success_count > 0) {
        header('Location: ../customer_survey.php?customer_id=' . $customer_id . '&success=1&saved=' . $success_count);
    } else {
        header('Location: ../customer_survey.php?customer_id=' . $customer_id . '&error=no_responses');
    }
} else {
    header('Location: ../index.php?error=invalid_request');
}
?>
