<?php
require_once __DIR__ . '/../models/Customer.php';

if ($_POST && isset($_POST['customer_id'])) {
    $customer_id = $_POST['customer_id'];
    $name = trim($_POST['name']);
    $email = trim($_POST['email']);
    $phone = trim($_POST['phone']);
    $address = trim($_POST['address']);
    
    // التحقق من صحة البيانات
    if (empty($name) || empty($email)) {
        header('Location: ../edit.php?id=' . $customer_id . '&error=missing_data');
        exit;
    }
    
    if (strlen($name) < 2) {
        header('Location: ../edit.php?id=' . $customer_id . '&error=invalid_name');
        exit;
    }
    
    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        header('Location: ../edit.php?id=' . $customer_id . '&error=invalid_email');
        exit;
    }
    
    $customer = new Customer();
    
    // التحقق من عدم تكرار البريد الإلكتروني
    $existing_customers = $customer->getAll();
    foreach ($existing_customers as $existing) {
        if ($existing['email'] === $email && $existing['id'] != $customer_id) {
            header('Location: ../edit.php?id=' . $customer_id . '&error=email_exists');
            exit;
        }
    }
    
    // تحديث البيانات
    $result = $customer->update($customer_id, $name, $email, $phone, $address);
    
    if ($result) {
        header('Location: ../edit.php?id=' . $customer_id . '&success=1');
    } else {
        header('Location: ../edit.php?id=' . $customer_id . '&error=update_failed');
    }
} else {
    header('Location: ../index.php?error=invalid_request');
}
?>
