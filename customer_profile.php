<?php
require_once __DIR__ . '/models/Customer.php';
require_once __DIR__ . '/models/CustomerRating.php';
require_once __DIR__ . '/models/CustomerNote.php';
require_once __DIR__ . '/models/CustomerInteraction.php';
require_once __DIR__ . '/models/CustomerFeedback.php';

if (!isset($_GET['id'])) {
    header('Location: index.php');
    exit;
}

$customer_id = $_GET['id'];
$customer = new Customer();
$rating = new CustomerRating();
$note = new CustomerNote();
$interaction = new CustomerInteraction();
$feedback = new CustomerFeedback();

$customer_data = $customer->getById($customer_id);
if (!$customer_data) {
    header('Location: index.php?error=customer_not_found');
    exit;
}

$ratings = $rating->getByCustomerId($customer_id);
$notes = $note->getByCustomerId($customer_id);
$interactions = $interaction->getByCustomerId($customer_id);
$feedbacks = $feedback->getByCustomerId($customer_id);
$avg_rating = $rating->getAverageRating($customer_id);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ملف العميل - <?= htmlspecialchars($customer_data['name']) ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .rating-stars {
            color: #ffc107;
        }
        .priority-high { border-left: 4px solid #dc3545; }
        .priority-medium { border-left: 4px solid #ffc107; }
        .priority-low { border-left: 4px solid #28a745; }
        .priority-urgent { border-left: 4px solid #6f42c1; }
        .status-open { background-color: #fff3cd; }
        .status-in-progress { background-color: #d1ecf1; }
        .status-resolved { background-color: #d4edda; }
        .status-closed { background-color: #f8d7da; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <h1><i class="fas fa-user-circle"></i> ملف العميل</h1>
                    <a href="index.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-right"></i> العودة للقائمة الرئيسية
                    </a>
                </div>
            </div>
        </div>

        <!-- Customer Info Card -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5><i class="fas fa-info-circle"></i> معلومات العميل</h5>
                    </div>
                    <div class="card-body">
                        <p><strong>الاسم:</strong> <?= htmlspecialchars($customer_data['name']) ?></p>
                        <p><strong>البريد الإلكتروني:</strong> <?= htmlspecialchars($customer_data['email']) ?></p>
                        <p><strong>الهاتف:</strong> <?= htmlspecialchars($customer_data['phone']) ?></p>
                        <p><strong>العنوان:</strong> <?= htmlspecialchars($customer_data['address']) ?></p>
                        <p><strong>تاريخ التسجيل:</strong> <?= date('Y-m-d H:i', strtotime($customer_data['created_at'])) ?></p>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-warning text-dark">
                        <h5><i class="fas fa-star"></i> التقييم العام</h5>
                    </div>
                    <div class="card-body text-center">
                        <?php if ($avg_rating['total_ratings'] > 0): ?>
                            <h2 class="rating-stars">
                                <?php 
                                $stars = round($avg_rating['avg_rating']);
                                for ($i = 1; $i <= 5; $i++) {
                                    echo $i <= $stars ? '<i class="fas fa-star"></i>' : '<i class="far fa-star"></i>';
                                }
                                ?>
                            </h2>
                            <p><?= number_format($avg_rating['avg_rating'], 1) ?> من 5</p>
                            <small class="text-muted">(<?= $avg_rating['total_ratings'] ?> تقييم)</small>
                        <?php else: ?>
                            <p class="text-muted">لا توجد تقييمات بعد</p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Navigation Tabs -->
        <ul class="nav nav-tabs" id="customerTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="ratings-tab" data-bs-toggle="tab" data-bs-target="#ratings" type="button">
                    <i class="fas fa-star"></i> التقييمات
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="notes-tab" data-bs-toggle="tab" data-bs-target="#notes" type="button">
                    <i class="fas fa-sticky-note"></i> الملاحظات
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="interactions-tab" data-bs-toggle="tab" data-bs-target="#interactions" type="button">
                    <i class="fas fa-comments"></i> التفاعلات
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="feedback-tab" data-bs-toggle="tab" data-bs-target="#feedback" type="button">
                    <i class="fas fa-comment-dots"></i> الشكاوى والاقتراحات
                </button>
            </li>
        </ul>

        <!-- Tab Content -->
        <div class="tab-content mt-3" id="customerTabsContent">
            <!-- Ratings Tab -->
            <div class="tab-pane fade show active" id="ratings" role="tabpanel">
                <div class="row">
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h6>إضافة تقييم جديد</h6>
                            </div>
                            <div class="card-body">
                                <form action="actions/add_rating.php" method="POST">
                                    <input type="hidden" name="customer_id" value="<?= $customer_id ?>">
                                    <div class="mb-3">
                                        <label class="form-label">التقييم</label>
                                        <select class="form-select" name="rating" required>
                                            <option value="">اختر التقييم</option>
                                            <option value="5">5 نجوم - ممتاز</option>
                                            <option value="4">4 نجوم - جيد جداً</option>
                                            <option value="3">3 نجوم - جيد</option>
                                            <option value="2">2 نجمة - مقبول</option>
                                            <option value="1">1 نجمة - ضعيف</option>
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">الفئة</label>
                                        <select class="form-select" name="category">
                                            <option value="general">عام</option>
                                            <option value="service">الخدمة</option>
                                            <option value="product">المنتج</option>
                                            <option value="support">الدعم الفني</option>
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">التعليق</label>
                                        <textarea class="form-control" name="comment" rows="3"></textarea>
                                    </div>
                                    <button type="submit" class="btn btn-primary">إضافة التقييم</button>
                                </form>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header">
                                <h6>تاريخ التقييمات</h6>
                            </div>
                            <div class="card-body">
                                <?php if (empty($ratings)): ?>
                                    <p class="text-muted">لا توجد تقييمات بعد</p>
                                <?php else: ?>
                                    <?php foreach ($ratings as $r): ?>
                                        <div class="border-bottom pb-3 mb-3">
                                            <div class="d-flex justify-content-between">
                                                <div class="rating-stars">
                                                    <?php for ($i = 1; $i <= 5; $i++): ?>
                                                        <?= $i <= $r['rating'] ? '<i class="fas fa-star"></i>' : '<i class="far fa-star"></i>' ?>
                                                    <?php endfor; ?>
                                                </div>
                                                <small class="text-muted"><?= date('Y-m-d H:i', strtotime($r['created_at'])) ?></small>
                                            </div>
                                            <p class="mb-1"><strong>الفئة:</strong> <?= htmlspecialchars($r['category']) ?></p>
                                            <?php if ($r['comment']): ?>
                                                <p class="mb-0"><?= htmlspecialchars($r['comment']) ?></p>
                                            <?php endif; ?>
                                        </div>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Notes Tab -->
            <div class="tab-pane fade" id="notes" role="tabpanel">
                <div class="row">
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h6>إضافة ملاحظة جديدة</h6>
                            </div>
                            <div class="card-body">
                                <form action="actions/add_note.php" method="POST">
                                    <input type="hidden" name="customer_id" value="<?= $customer_id ?>">
                                    <div class="mb-3">
                                        <label class="form-label">نوع الملاحظة</label>
                                        <select class="form-select" name="note_type">
                                            <option value="general">عامة</option>
                                            <option value="important">مهمة</option>
                                            <option value="follow_up">متابعة</option>
                                            <option value="reminder">تذكير</option>
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">الأولوية</label>
                                        <select class="form-select" name="priority">
                                            <option value="low">منخفضة</option>
                                            <option value="medium" selected>متوسطة</option>
                                            <option value="high">عالية</option>
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">الملاحظة</label>
                                        <textarea class="form-control" name="note" rows="4" required></textarea>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">كاتب الملاحظة</label>
                                        <input type="text" class="form-control" name="created_by" value="المدير">
                                    </div>
                                    <button type="submit" class="btn btn-primary">إضافة الملاحظة</button>
                                </form>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header">
                                <h6>الملاحظات</h6>
                            </div>
                            <div class="card-body">
                                <?php if (empty($notes)): ?>
                                    <p class="text-muted">لا توجد ملاحظات بعد</p>
                                <?php else: ?>
                                    <?php foreach ($notes as $n): ?>
                                        <div class="card mb-3 priority-<?= $n['priority'] ?>">
                                            <div class="card-body">
                                                <div class="d-flex justify-content-between">
                                                    <h6 class="card-title">
                                                        <span class="badge bg-secondary"><?= htmlspecialchars($n['note_type']) ?></span>
                                                        <span class="badge bg-<?= $n['priority'] == 'high' ? 'danger' : ($n['priority'] == 'medium' ? 'warning' : 'success') ?>">
                                                            <?= $n['priority'] == 'high' ? 'عالية' : ($n['priority'] == 'medium' ? 'متوسطة' : 'منخفضة') ?>
                                                        </span>
                                                    </h6>
                                                    <small class="text-muted"><?= date('Y-m-d H:i', strtotime($n['created_at'])) ?></small>
                                                </div>
                                                <p class="card-text"><?= nl2br(htmlspecialchars($n['note'])) ?></p>
                                                <small class="text-muted">بواسطة: <?= htmlspecialchars($n['created_by']) ?></small>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Interactions Tab -->
            <div class="tab-pane fade" id="interactions" role="tabpanel">
                <div class="row">
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h6>إضافة تفاعل جديد</h6>
                            </div>
                            <div class="card-body">
                                <form action="actions/add_interaction.php" method="POST">
                                    <input type="hidden" name="customer_id" value="<?= $customer_id ?>">
                                    <div class="mb-3">
                                        <label class="form-label">نوع التفاعل</label>
                                        <select class="form-select" name="interaction_type" required>
                                            <option value="">اختر النوع</option>
                                            <option value="call">مكالمة هاتفية</option>
                                            <option value="meeting">اجتماع</option>
                                            <option value="email">بريد إلكتروني</option>
                                            <option value="chat">محادثة</option>
                                            <option value="visit">زيارة</option>
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">الموضوع</label>
                                        <input type="text" class="form-control" name="subject" required>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">الوصف</label>
                                        <textarea class="form-control" name="description" rows="3"></textarea>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">المدة (بالدقائق)</label>
                                        <input type="number" class="form-control" name="duration_minutes" min="0">
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">النتيجة</label>
                                        <select class="form-select" name="outcome">
                                            <option value="successful">ناجح</option>
                                            <option value="pending">معلق</option>
                                            <option value="failed">فاشل</option>
                                            <option value="follow_up_needed">يحتاج متابعة</option>
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">الإجراء التالي</label>
                                        <textarea class="form-control" name="next_action" rows="2"></textarea>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">تاريخ التفاعل</label>
                                        <input type="datetime-local" class="form-control" name="interaction_date" value="<?= date('Y-m-d\TH:i') ?>">
                                    </div>
                                    <button type="submit" class="btn btn-primary">إضافة التفاعل</button>
                                </form>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header">
                                <h6>تاريخ التفاعلات</h6>
                            </div>
                            <div class="card-body">
                                <?php if (empty($interactions)): ?>
                                    <p class="text-muted">لا توجد تفاعلات بعد</p>
                                <?php else: ?>
                                    <?php foreach ($interactions as $i): ?>
                                        <div class="card mb-3">
                                            <div class="card-body">
                                                <div class="d-flex justify-content-between">
                                                    <h6 class="card-title">
                                                        <i class="fas fa-<?= $i['interaction_type'] == 'call' ? 'phone' : ($i['interaction_type'] == 'meeting' ? 'users' : ($i['interaction_type'] == 'email' ? 'envelope' : ($i['interaction_type'] == 'chat' ? 'comment' : 'map-marker-alt'))) ?>"></i>
                                                        <?= htmlspecialchars($i['subject']) ?>
                                                    </h6>
                                                    <span class="badge bg-<?= $i['outcome'] == 'successful' ? 'success' : ($i['outcome'] == 'pending' ? 'warning' : ($i['outcome'] == 'failed' ? 'danger' : 'info')) ?>">
                                                        <?= $i['outcome'] == 'successful' ? 'ناجح' : ($i['outcome'] == 'pending' ? 'معلق' : ($i['outcome'] == 'failed' ? 'فاشل' : 'يحتاج متابعة')) ?>
                                                    </span>
                                                </div>
                                                <p class="card-text"><?= nl2br(htmlspecialchars($i['description'])) ?></p>
                                                <div class="row">
                                                    <div class="col-md-6">
                                                        <small class="text-muted">
                                                            <i class="fas fa-clock"></i> <?= date('Y-m-d H:i', strtotime($i['interaction_date'])) ?>
                                                        </small>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <?php if ($i['duration_minutes'] > 0): ?>
                                                            <small class="text-muted">
                                                                <i class="fas fa-stopwatch"></i> <?= $i['duration_minutes'] ?> دقيقة
                                                            </small>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                                <?php if ($i['next_action']): ?>
                                                    <div class="mt-2">
                                                        <strong>الإجراء التالي:</strong> <?= htmlspecialchars($i['next_action']) ?>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Feedback Tab -->
            <div class="tab-pane fade" id="feedback" role="tabpanel">
                <div class="row">
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h6>إضافة شكوى أو اقتراح</h6>
                            </div>
                            <div class="card-body">
                                <form action="actions/add_feedback.php" method="POST">
                                    <input type="hidden" name="customer_id" value="<?= $customer_id ?>">
                                    <div class="mb-3">
                                        <label class="form-label">النوع</label>
                                        <select class="form-select" name="feedback_type" required>
                                            <option value="">اختر النوع</option>
                                            <option value="complaint">شكوى</option>
                                            <option value="suggestion">اقتراح</option>
                                            <option value="compliment">إطراء</option>
                                            <option value="inquiry">استفسار</option>
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">العنوان</label>
                                        <input type="text" class="form-control" name="title" required>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">الوصف</label>
                                        <textarea class="form-control" name="description" rows="4" required></textarea>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">الأولوية</label>
                                        <select class="form-select" name="priority">
                                            <option value="low">منخفضة</option>
                                            <option value="medium" selected>متوسطة</option>
                                            <option value="high">عالية</option>
                                            <option value="urgent">عاجلة</option>
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">مسؤول المتابعة</label>
                                        <input type="text" class="form-control" name="assigned_to">
                                    </div>
                                    <button type="submit" class="btn btn-primary">إضافة</button>
                                </form>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header">
                                <h6>الشكاوى والاقتراحات</h6>
                            </div>
                            <div class="card-body">
                                <?php if (empty($feedbacks)): ?>
                                    <p class="text-muted">لا توجد شكاوى أو اقتراحات بعد</p>
                                <?php else: ?>
                                    <?php foreach ($feedbacks as $f): ?>
                                        <div class="card mb-3 priority-<?= $f['priority'] ?> status-<?= $f['status'] ?>">
                                            <div class="card-body">
                                                <div class="d-flex justify-content-between">
                                                    <h6 class="card-title">
                                                        <span class="badge bg-<?= $f['feedback_type'] == 'complaint' ? 'danger' : ($f['feedback_type'] == 'suggestion' ? 'info' : ($f['feedback_type'] == 'compliment' ? 'success' : 'warning')) ?>">
                                                            <?= $f['feedback_type'] == 'complaint' ? 'شكوى' : ($f['feedback_type'] == 'suggestion' ? 'اقتراح' : ($f['feedback_type'] == 'compliment' ? 'إطراء' : 'استفسار')) ?>
                                                        </span>
                                                        <?= htmlspecialchars($f['title']) ?>
                                                    </h6>
                                                    <div>
                                                        <span class="badge bg-<?= $f['priority'] == 'urgent' ? 'dark' : ($f['priority'] == 'high' ? 'danger' : ($f['priority'] == 'medium' ? 'warning' : 'success')) ?>">
                                                            <?= $f['priority'] == 'urgent' ? 'عاجل' : ($f['priority'] == 'high' ? 'عالي' : ($f['priority'] == 'medium' ? 'متوسط' : 'منخفض')) ?>
                                                        </span>
                                                        <span class="badge bg-<?= $f['status'] == 'open' ? 'secondary' : ($f['status'] == 'in_progress' ? 'primary' : ($f['status'] == 'resolved' ? 'success' : 'dark')) ?>">
                                                            <?= $f['status'] == 'open' ? 'مفتوح' : ($f['status'] == 'in_progress' ? 'قيد المعالجة' : ($f['status'] == 'resolved' ? 'محلول' : 'مغلق')) ?>
                                                        </span>
                                                    </div>
                                                </div>
                                                <p class="card-text"><?= nl2br(htmlspecialchars($f['description'])) ?></p>
                                                <?php if ($f['assigned_to']): ?>
                                                    <p><strong>مسؤول المتابعة:</strong> <?= htmlspecialchars($f['assigned_to']) ?></p>
                                                <?php endif; ?>
                                                <?php if ($f['resolution']): ?>
                                                    <div class="alert alert-success">
                                                        <strong>الحل:</strong> <?= nl2br(htmlspecialchars($f['resolution'])) ?>
                                                        <br><small>تم الحل في: <?= date('Y-m-d H:i', strtotime($f['resolved_at'])) ?></small>
                                                    </div>
                                                <?php endif; ?>
                                                <small class="text-muted">تم الإنشاء في: <?= date('Y-m-d H:i', strtotime($f['created_at'])) ?></small>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
