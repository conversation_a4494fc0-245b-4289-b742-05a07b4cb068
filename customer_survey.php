<?php
require_once __DIR__ . '/models/Customer.php';
require_once __DIR__ . '/models/SurveyQuestion.php';
require_once __DIR__ . '/models/CustomerSurveyResponse.php';

if (!isset($_GET['customer_id'])) {
    header('Location: index.php?error=missing_customer_id');
    exit;
}

$customer_id = $_GET['customer_id'];
$customer = new Customer();
$surveyQuestion = new SurveyQuestion();
$surveyResponse = new CustomerSurveyResponse();

$customer_data = $customer->getById($customer_id);
if (!$customer_data) {
    header('Location: index.php?error=customer_not_found');
    exit;
}

$questions = $surveyQuestion->getAllActive();
$existing_responses = $surveyResponse->getCustomerResponses($customer_id);

// تحويل الإجابات الموجودة إلى مصفوفة للوصول السهل
$responses_by_question = [];
foreach ($existing_responses as $response) {
    $responses_by_question[$response['question_id']] = $response;
}

$has_completed = $surveyResponse->hasCustomerCompletedSurvey($customer_id);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>استبيان تقييم الخدمة - <?= htmlspecialchars($customer_data['name']) ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .star-rating {
            direction: ltr;
            display: inline-block;
        }
        .star-rating input[type="radio"] {
            display: none;
        }
        .star-rating label {
            color: #ddd;
            font-size: 2rem;
            cursor: pointer;
            transition: color 0.2s;
        }
        .star-rating label:hover,
        .star-rating label:hover ~ label,
        .star-rating input[type="radio"]:checked ~ label {
            color: #ffc107;
        }
        .star-rating input[type="radio"]:checked + label {
            color: #ffc107;
        }
        .question-card {
            transition: transform 0.2s;
            border: 2px solid transparent;
        }
        .question-card:hover {
            transform: translateY(-2px);
            border-color: #007bff;
        }
        .completed-survey {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
        }
        .progress-bar-custom {
            height: 10px;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <div class="d-flex justify-content-between align-items-center">
                            <h4><i class="fas fa-clipboard-list"></i> استبيان تقييم الخدمة</h4>
                            <a href="customer_profile.php?id=<?= $customer_id ?>" class="btn btn-light btn-sm">
                                <i class="fas fa-user"></i> ملف العميل
                            </a>
                        </div>
                    </div>
                    <div class="card-body">
                        <h5>مرحباً <?= htmlspecialchars($customer_data['name']) ?></h5>
                        <p class="mb-0">نقدر وقتك في تقييم خدماتنا. رأيك مهم جداً لنا لتحسين مستوى الخدمة.</p>
                        
                        <!-- Progress Bar -->
                        <?php 
                        $answered_count = count($existing_responses);
                        $total_questions = count($questions);
                        $progress_percentage = $total_questions > 0 ? ($answered_count / $total_questions) * 100 : 0;
                        ?>
                        <div class="mt-3">
                            <div class="d-flex justify-content-between mb-1">
                                <span>التقدم: <?= $answered_count ?> من <?= $total_questions ?></span>
                                <span><?= round($progress_percentage) ?>%</span>
                            </div>
                            <div class="progress progress-bar-custom">
                                <div class="progress-bar bg-success" style="width: <?= $progress_percentage ?>%"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <?php if ($has_completed): ?>
            <!-- Survey Completed Message -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card completed-survey">
                        <div class="card-body text-center">
                            <i class="fas fa-check-circle fa-3x mb-3"></i>
                            <h3>شكراً لك!</h3>
                            <p class="mb-0">لقد أكملت الاستبيان بنجاح. نقدر وقتك وآراءك القيمة.</p>
                            <div class="mt-3">
                                <a href="customer_profile.php?id=<?= $customer_id ?>" class="btn btn-light me-2">
                                    <i class="fas fa-user"></i> ملف العميل
                                </a>
                                <a href="index.php" class="btn btn-outline-light">
                                    <i class="fas fa-home"></i> الصفحة الرئيسية
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>

        <!-- Survey Form -->
        <form id="surveyForm" action="actions/save_survey_response.php" method="POST">
            <input type="hidden" name="customer_id" value="<?= $customer_id ?>">
            
            <?php foreach ($questions as $index => $question): ?>
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card question-card">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <span class="badge bg-primary me-2"><?= $index + 1 ?></span>
                                    <?= htmlspecialchars($question['question_text']) ?>
                                    <?php if (isset($responses_by_question[$question['id']])): ?>
                                        <i class="fas fa-check-circle text-success float-end"></i>
                                    <?php endif; ?>
                                </h6>
                            </div>
                            <div class="card-body">
                                <?php if ($question['question_type'] == 'rating'): ?>
                                    <!-- Star Rating -->
                                    <div class="star-rating">
                                        <?php 
                                        $current_rating = isset($responses_by_question[$question['id']]) ? 
                                                         $responses_by_question[$question['id']]['rating_value'] : 0;
                                        ?>
                                        <?php for ($i = 5; $i >= 1; $i--): ?>
                                            <input type="radio" 
                                                   id="rating_<?= $question['id'] ?>_<?= $i ?>" 
                                                   name="rating_<?= $question['id'] ?>" 
                                                   value="<?= $i ?>"
                                                   <?= $current_rating == $i ? 'checked' : '' ?>>
                                            <label for="rating_<?= $question['id'] ?>_<?= $i ?>">
                                                <i class="fas fa-star"></i>
                                            </label>
                                        <?php endfor; ?>
                                    </div>
                                    <div class="mt-2">
                                        <small class="text-muted">
                                            <span class="float-start">ضعيف</span>
                                            <span class="float-end">ممتاز</span>
                                        </small>
                                        <div class="clearfix"></div>
                                    </div>
                                    
                                <?php elseif ($question['question_type'] == 'text'): ?>
                                    <!-- Text Response -->
                                    <textarea class="form-control" 
                                              name="text_<?= $question['id'] ?>" 
                                              rows="4" 
                                              placeholder="اكتب إجابتك هنا..."><?= isset($responses_by_question[$question['id']]) ? htmlspecialchars($responses_by_question[$question['id']]['text_value']) : '' ?></textarea>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>

            <!-- Submit Button -->
            <div class="row">
                <div class="col-12 text-center">
                    <button type="submit" class="btn btn-success btn-lg">
                        <i class="fas fa-paper-plane"></i> حفظ الإجابات
                    </button>
                    <div class="mt-2">
                        <small class="text-muted">يمكنك تعديل إجاباتك في أي وقت</small>
                    </div>
                </div>
            </div>
        </form>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Auto-save functionality
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('surveyForm');
            const inputs = form.querySelectorAll('input[type="radio"], textarea');
            
            inputs.forEach(input => {
                input.addEventListener('change', function() {
                    // Auto-save after 1 second delay
                    setTimeout(() => {
                        saveResponse(this);
                    }, 1000);
                });
            });
        });

        function saveResponse(element) {
            const formData = new FormData();
            formData.append('customer_id', <?= $customer_id ?>);
            
            if (element.type === 'radio') {
                const questionId = element.name.split('_')[1];
                formData.append('question_id', questionId);
                formData.append('rating_value', element.value);
            } else if (element.tagName === 'TEXTAREA') {
                const questionId = element.name.split('_')[1];
                formData.append('question_id', questionId);
                formData.append('text_value', element.value);
            }
            
            fetch('actions/save_single_response.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Update progress bar
                    location.reload();
                }
            })
            .catch(error => console.error('Error:', error));
        }
    </script>
</body>
</html>
