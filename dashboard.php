<?php
require_once 'models/Customer.php';
require_once 'models/CustomerRating.php';
require_once 'models/CustomerNote.php';
require_once 'models/CustomerInteraction.php';
require_once 'models/CustomerFeedback.php';

$customer = new Customer();
$rating = new CustomerRating();
$note = new CustomerNote();
$interaction = new CustomerInteraction();
$feedback = new CustomerFeedback();

// Get statistics
$total_customers = count($customer->getAll());
$avg_rating_data = $rating->getAverageRating();
$total_notes = $note->getNotesCount();
$interaction_stats = $interaction->getInteractionStats();
$feedback_stats = $feedback->getFeedbackStats();
$open_feedback = $feedback->getOpenFeedback();
$follow_up_needed = $interaction->getFollowUpNeeded();
$rating_categories = $rating->getRatingsByCategory();

// Recent activities
$recent_ratings = array_slice($rating->getAll(), 0, 5);
$recent_interactions = array_slice($interaction->getAll(), 0, 5);
$recent_feedback = array_slice($feedback->getAll(), 0, 5);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة تحكم تجربة العملاء</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .stat-card {
            transition: transform 0.2s;
        }
        .stat-card:hover {
            transform: translateY(-5px);
        }
        .rating-stars {
            color: #ffc107;
        }
        .priority-urgent { border-left: 4px solid #6f42c1; }
        .priority-high { border-left: 4px solid #dc3545; }
        .priority-medium { border-left: 4px solid #ffc107; }
        .priority-low { border-left: 4px solid #28a745; }
    </style>
</head>
<body>
    <div class="container-fluid mt-4">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <h1><i class="fas fa-chart-line"></i> لوحة تحكم تجربة العملاء</h1>
                    <div>
                        <a href="index.php" class="btn btn-secondary me-2">
                            <i class="fas fa-users"></i> إدارة العملاء
                        </a>
                        <a href="reports.php" class="btn btn-info">
                            <i class="fas fa-file-alt"></i> التقارير
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card stat-card bg-primary text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4><?= $total_customers ?></h4>
                                <p class="mb-0">إجمالي العملاء</p>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-users fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stat-card bg-warning text-dark">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4><?= $avg_rating_data['avg_rating'] ? number_format($avg_rating_data['avg_rating'], 1) : '0.0' ?></h4>
                                <p class="mb-0">متوسط التقييم</p>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-star fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stat-card bg-info text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4><?= count($open_feedback) ?></h4>
                                <p class="mb-0">الشكاوى المفتوحة</p>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-exclamation-triangle fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stat-card bg-success text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4><?= count($follow_up_needed) ?></h4>
                                <p class="mb-0">يحتاج متابعة</p>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-clock fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts Row -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-chart-pie"></i> توزيع أنواع التفاعلات</h5>
                    </div>
                    <div class="card-body">
                        <canvas id="interactionChart"></canvas>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-chart-bar"></i> توزيع الشكاوى والاقتراحات</h5>
                    </div>
                    <div class="card-body">
                        <canvas id="feedbackChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Activities -->
        <div class="row">
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h6><i class="fas fa-star"></i> أحدث التقييمات</h6>
                    </div>
                    <div class="card-body">
                        <?php if (empty($recent_ratings)): ?>
                            <p class="text-muted">لا توجد تقييمات حديثة</p>
                        <?php else: ?>
                            <?php foreach ($recent_ratings as $r): ?>
                                <div class="border-bottom pb-2 mb-2">
                                    <div class="d-flex justify-content-between">
                                        <strong><?= htmlspecialchars($r['customer_name']) ?></strong>
                                        <div class="rating-stars">
                                            <?php for ($i = 1; $i <= 5; $i++): ?>
                                                <?= $i <= $r['rating'] ? '<i class="fas fa-star"></i>' : '<i class="far fa-star"></i>' ?>
                                            <?php endfor; ?>
                                        </div>
                                    </div>
                                    <small class="text-muted"><?= date('Y-m-d H:i', strtotime($r['created_at'])) ?></small>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h6><i class="fas fa-comments"></i> أحدث التفاعلات</h6>
                    </div>
                    <div class="card-body">
                        <?php if (empty($recent_interactions)): ?>
                            <p class="text-muted">لا توجد تفاعلات حديثة</p>
                        <?php else: ?>
                            <?php foreach ($recent_interactions as $i): ?>
                                <div class="border-bottom pb-2 mb-2">
                                    <div class="d-flex justify-content-between">
                                        <strong><?= htmlspecialchars($i['customer_name']) ?></strong>
                                        <span class="badge bg-<?= $i['outcome'] == 'successful' ? 'success' : ($i['outcome'] == 'pending' ? 'warning' : 'danger') ?>">
                                            <?= $i['interaction_type'] ?>
                                        </span>
                                    </div>
                                    <p class="mb-1"><?= htmlspecialchars($i['subject']) ?></p>
                                    <small class="text-muted"><?= date('Y-m-d H:i', strtotime($i['interaction_date'])) ?></small>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h6><i class="fas fa-exclamation-circle"></i> الشكاوى المفتوحة</h6>
                    </div>
                    <div class="card-body">
                        <?php if (empty($open_feedback)): ?>
                            <p class="text-muted">لا توجد شكاوى مفتوحة</p>
                        <?php else: ?>
                            <?php foreach (array_slice($open_feedback, 0, 5) as $f): ?>
                                <div class="border-bottom pb-2 mb-2 priority-<?= $f['priority'] ?>">
                                    <div class="d-flex justify-content-between">
                                        <strong><?= htmlspecialchars($f['customer_name']) ?></strong>
                                        <span class="badge bg-<?= $f['priority'] == 'urgent' ? 'dark' : ($f['priority'] == 'high' ? 'danger' : 'warning') ?>">
                                            <?= $f['priority'] ?>
                                        </span>
                                    </div>
                                    <p class="mb-1"><?= htmlspecialchars($f['title']) ?></p>
                                    <small class="text-muted"><?= date('Y-m-d H:i', strtotime($f['created_at'])) ?></small>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Interaction Types Chart
        const interactionCtx = document.getElementById('interactionChart').getContext('2d');
        const interactionData = {
            labels: [
                <?php foreach ($interaction_stats as $stat): ?>
                    '<?= $stat['interaction_type'] == 'call' ? 'مكالمات' : ($stat['interaction_type'] == 'meeting' ? 'اجتماعات' : ($stat['interaction_type'] == 'email' ? 'إيميلات' : ($stat['interaction_type'] == 'chat' ? 'محادثات' : 'زيارات'))) ?>',
                <?php endforeach; ?>
            ],
            datasets: [{
                data: [
                    <?php foreach ($interaction_stats as $stat): ?>
                        <?= $stat['count'] ?>,
                    <?php endforeach; ?>
                ],
                backgroundColor: [
                    '#FF6384',
                    '#36A2EB',
                    '#FFCE56',
                    '#4BC0C0',
                    '#9966FF'
                ]
            }]
        };

        new Chart(interactionCtx, {
            type: 'doughnut',
            data: interactionData,
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });

        // Feedback Types Chart
        const feedbackCtx = document.getElementById('feedbackChart').getContext('2d');

        // Process feedback stats for chart
        const feedbackTypes = {};
        <?php foreach ($feedback_stats as $stat): ?>
            if (!feedbackTypes['<?= $stat['feedback_type'] ?>']) {
                feedbackTypes['<?= $stat['feedback_type'] ?>'] = 0;
            }
            feedbackTypes['<?= $stat['feedback_type'] ?>'] += <?= $stat['count'] ?>;
        <?php endforeach; ?>

        const feedbackLabels = [];
        const feedbackCounts = [];

        for (const [type, count] of Object.entries(feedbackTypes)) {
            let label = '';
            switch(type) {
                case 'complaint': label = 'شكاوى'; break;
                case 'suggestion': label = 'اقتراحات'; break;
                case 'compliment': label = 'إطراءات'; break;
                case 'inquiry': label = 'استفسارات'; break;
                default: label = type;
            }
            feedbackLabels.push(label);
            feedbackCounts.push(count);
        }

        const feedbackData = {
            labels: feedbackLabels,
            datasets: [{
                label: 'العدد',
                data: feedbackCounts,
                backgroundColor: [
                    '#FF6384',
                    '#36A2EB',
                    '#FFCE56',
                    '#4BC0C0'
                ],
                borderWidth: 1
            }]
        };

        new Chart(feedbackCtx, {
            type: 'bar',
            data: feedbackData,
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });
    </script>
</body>
</html>
