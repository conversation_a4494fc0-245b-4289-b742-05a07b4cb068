CREATE DATABASE IF NOT EXISTS customer_db;
USE customer_db;

CREATE TABLE customers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    phone VARCHAR(20),
    address TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول فئات التقييم
CREATE TABLE rating_categories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول تقييمات العملاء
CREATE TABLE customer_ratings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    customer_id INT NOT NULL,
    rating INT NOT NULL CHECK (rating >= 1 AND rating <= 5),
    comment TEXT,
    category_id INT DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOR<PERSON>GN KEY (customer_id) REFERENCES customers(id) ON DELETE CASCADE,
    FOREIGN KEY (category_id) REFERENCES rating_categories(id) ON DELETE SET NULL
);

-- جدول ملاحظات العملاء
CREATE TABLE customer_notes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    customer_id INT NOT NULL,
    note TEXT NOT NULL,
    note_type VARCHAR(50) DEFAULT 'general', -- general, important, follow_up, reminder
    priority ENUM('low', 'medium', 'high') DEFAULT 'medium',
    created_by VARCHAR(100) DEFAULT 'system',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE CASCADE
);

-- جدول تفاعلات العملاء
CREATE TABLE customer_interactions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    customer_id INT NOT NULL,
    interaction_type ENUM('call', 'meeting', 'email', 'chat', 'visit') NOT NULL,
    subject VARCHAR(200) NOT NULL,
    description TEXT,
    duration_minutes INT DEFAULT 0,
    outcome ENUM('successful', 'pending', 'failed', 'follow_up_needed') DEFAULT 'successful',
    next_action TEXT,
    interaction_date DATETIME NOT NULL,
    created_by VARCHAR(100) DEFAULT 'system',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE CASCADE
);

-- جدول الشكاوى والاقتراحات
CREATE TABLE customer_feedback (
    id INT AUTO_INCREMENT PRIMARY KEY,
    customer_id INT NOT NULL,
    feedback_type ENUM('complaint', 'suggestion', 'compliment', 'inquiry') NOT NULL,
    title VARCHAR(200) NOT NULL,
    description TEXT NOT NULL,
    priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium',
    status ENUM('open', 'in_progress', 'resolved', 'closed') DEFAULT 'open',
    assigned_to VARCHAR(100),
    resolution TEXT,
    resolved_at DATETIME NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE CASCADE
);

-- جدول الأسئلة
CREATE TABLE survey_questions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    question_text TEXT NOT NULL,
    question_type ENUM('rating', 'text', 'multiple_choice') DEFAULT 'rating',
    is_active BOOLEAN DEFAULT TRUE,
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول إجابات العملاء على الأسئلة
CREATE TABLE customer_survey_responses (
    id INT AUTO_INCREMENT PRIMARY KEY,
    customer_id INT NOT NULL,
    question_id INT NOT NULL,
    rating_value INT NULL, -- للأسئلة من نوع rating (1-5)
    text_value TEXT NULL,  -- للأسئلة النصية
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE CASCADE,
    FOREIGN KEY (question_id) REFERENCES survey_questions(id) ON DELETE CASCADE,
    UNIQUE KEY unique_customer_question (customer_id, question_id)
);

-- إدراج فئات التقييم الافتراضية
INSERT INTO rating_categories (name, description, sort_order) VALUES
('عام', 'تقييم عام للخدمة', 1),
('جودة الخدمة', 'تقييم جودة الخدمة المقدمة', 2),
('سرعة الاستجابة', 'تقييم سرعة الرد والاستجابة', 3),
('الدعم الفني', 'تقييم مستوى الدعم الفني', 4),
('سهولة الاستخدام', 'تقييم سهولة استخدام الخدمة', 5),
('القيمة مقابل المال', 'تقييم القيمة مقابل السعر المدفوع', 6);

-- إدراج أسئلة افتراضية
INSERT INTO survey_questions (question_text, question_type, sort_order) VALUES
('كيف تقيم جودة خدماتنا بشكل عام؟', 'rating', 1),
('ما مدى رضاك عن سرعة الاستجابة؟', 'rating', 2),
('كيف تقيم مستوى الدعم الفني؟', 'rating', 3),
('ما مدى احتمالية أن توصي بخدماتنا للآخرين؟', 'rating', 4),
('ما هي اقتراحاتك لتحسين خدماتنا؟', 'text', 5);