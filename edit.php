<?php
require_once __DIR__ . '/models/Customer.php';

if (!isset($_GET['id'])) {
    header('Location: index.php?error=missing_id');
    exit;
}

$customer_id = $_GET['id'];
$customer = new Customer();
$customer_data = $customer->getById($customer_id);

if (!$customer_data) {
    header('Location: index.php?error=customer_not_found');
    exit;
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تعديل العميل - <?= htmlspecialchars($customer_data['name']) ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <h1><i class="fas fa-user-edit"></i> تعديل بيانات العميل</h1>
                    <div>
                        <a href="customer_profile.php?id=<?= $customer_id ?>" class="btn btn-info me-2">
                            <i class="fas fa-user"></i> ملف العميل
                        </a>
                        <a href="index.php" class="btn btn-secondary">
                            <i class="fas fa-arrow-right"></i> العودة للقائمة
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Success/Error Messages -->
        <?php if (isset($_GET['success'])): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle"></i> تم تحديث بيانات العميل بنجاح!
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if (isset($_GET['error'])): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-triangle"></i> 
                <?php
                switch($_GET['error']) {
                    case 'email_exists':
                        echo 'البريد الإلكتروني مستخدم من قبل عميل آخر';
                        break;
                    case 'update_failed':
                        echo 'فشل في تحديث البيانات';
                        break;
                    default:
                        echo 'حدث خطأ غير متوقع';
                }
                ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- Edit Form -->
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="mb-0"><i class="fas fa-edit"></i> تعديل بيانات العميل</h5>
                    </div>
                    <div class="card-body">
                        <form action="actions/update_customer.php" method="POST">
                            <input type="hidden" name="customer_id" value="<?= $customer_id ?>">
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">
                                            <i class="fas fa-user"></i> الاسم الكامل
                                        </label>
                                        <input type="text" 
                                               class="form-control" 
                                               name="name" 
                                               value="<?= htmlspecialchars($customer_data['name']) ?>" 
                                               required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">
                                            <i class="fas fa-envelope"></i> البريد الإلكتروني
                                        </label>
                                        <input type="email" 
                                               class="form-control" 
                                               name="email" 
                                               value="<?= htmlspecialchars($customer_data['email']) ?>" 
                                               required>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">
                                            <i class="fas fa-phone"></i> رقم الهاتف
                                        </label>
                                        <input type="tel" 
                                               class="form-control" 
                                               name="phone" 
                                               value="<?= htmlspecialchars($customer_data['phone']) ?>">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">
                                            <i class="fas fa-calendar"></i> تاريخ التسجيل
                                        </label>
                                        <input type="text" 
                                               class="form-control" 
                                               value="<?= date('Y-m-d H:i', strtotime($customer_data['created_at'])) ?>" 
                                               readonly>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">
                                    <i class="fas fa-map-marker-alt"></i> العنوان
                                </label>
                                <textarea class="form-control" 
                                          name="address" 
                                          rows="3"><?= htmlspecialchars($customer_data['address']) ?></textarea>
                            </div>

                            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                <a href="index.php" class="btn btn-secondary me-md-2">
                                    <i class="fas fa-times"></i> إلغاء
                                </a>
                                <button type="submit" class="btn btn-warning">
                                    <i class="fas fa-save"></i> حفظ التغييرات
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Customer Info Summary -->
                <div class="card mt-4">
                    <div class="card-header bg-info text-white">
                        <h6 class="mb-0"><i class="fas fa-info-circle"></i> معلومات إضافية</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4 text-center">
                                <h5 class="text-primary"><?= $customer_id ?></h5>
                                <small class="text-muted">رقم العميل</small>
                            </div>
                            <div class="col-md-4 text-center">
                                <h5 class="text-success">
                                    <?= floor((time() - strtotime($customer_data['created_at'])) / 86400) ?>
                                </h5>
                                <small class="text-muted">يوم منذ التسجيل</small>
                            </div>
                            <div class="col-md-4 text-center">
                                <div class="d-grid">
                                    <a href="customer_survey.php?customer_id=<?= $customer_id ?>" class="btn btn-success btn-sm">
                                        <i class="fas fa-clipboard-list"></i> الاستبيان
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Auto-dismiss alerts after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(alert => {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 5000);

        // Form validation
        document.querySelector('form').addEventListener('submit', function(e) {
            const name = document.querySelector('input[name="name"]').value.trim();
            const email = document.querySelector('input[name="email"]').value.trim();
            
            if (name.length < 2) {
                e.preventDefault();
                alert('يجب أن يكون الاسم أكثر من حرفين');
                return;
            }
            
            if (!email.includes('@')) {
                e.preventDefault();
                alert('يرجى إدخال بريد إلكتروني صحيح');
                return;
            }
        });
    </script>
</body>
</html>
