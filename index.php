<?php
require_once 'models/Customer.php';

$customer = new Customer();
$customers = $customer->getAll();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة العملاء</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <h1><i class="fas fa-users"></i> نظام إدارة تجربة العملاء</h1>
                    <div>
                        <a href="dashboard.php" class="btn btn-primary me-2">
                            <i class="fas fa-chart-line"></i> لوحة التحكم
                        </a>
                        <a href="reports.php" class="btn btn-info">
                            <i class="fas fa-file-alt"></i> التقارير
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Stats -->
        <?php
        require_once 'models/CustomerRating.php';
        require_once 'models/CustomerFeedback.php';
        $rating = new CustomerRating();
        $feedback = new CustomerFeedback();
        $avg_rating = $rating->getAverageRating();
        $open_feedback = $feedback->getByStatus('open');
        ?>
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card bg-primary text-white">
                    <div class="card-body text-center">
                        <h4><?= count($customers) ?></h4>
                        <p class="mb-0">إجمالي العملاء</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-warning text-dark">
                    <div class="card-body text-center">
                        <h4><?= $avg_rating['avg_rating'] ? number_format($avg_rating['avg_rating'], 1) : '0.0' ?></h4>
                        <p class="mb-0">متوسط التقييم</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-info text-white">
                    <div class="card-body text-center">
                        <h4><?= count($open_feedback) ?></h4>
                        <p class="mb-0">الشكاوى المفتوحة</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-success text-white">
                    <div class="card-body text-center">
                        <h4><?= $avg_rating['total_ratings'] ?></h4>
                        <p class="mb-0">إجمالي التقييمات</p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="col-md-4">
                <h3>إضافة عميل جديد</h3>
                <form action="actions/add_customer.php" method="POST">
                    <div class="mb-3">
                        <label class="form-label">الاسم</label>
                        <input type="text" class="form-control" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">البريد الإلكتروني</label>
                        <input type="email" class="form-control" name="email" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">الهاتف</label>
                        <input type="text" class="form-control" name="phone">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">العنوان</label>
                        <textarea class="form-control" name="address" rows="3"></textarea>
                    </div>
                    <button type="submit" class="btn btn-primary">إضافة العميل</button>
                </form>
            </div>
            
            <div class="col-md-8">
                <h3>قائمة العملاء</h3>
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>الرقم</th>
                            <th>الاسم</th>
                            <th>البريد الإلكتروني</th>
                            <th>الهاتف</th>
                            <th>التقييم</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach($customers as $cust): ?>
                        <?php
                        $customer_rating = $rating->getAverageRating($cust['id']);
                        ?>
                        <tr>
                            <td><?= $cust['id'] ?></td>
                            <td>
                                <a href="customer_profile.php?id=<?= $cust['id'] ?>" class="text-decoration-none">
                                    <?= htmlspecialchars($cust['name']) ?>
                                </a>
                            </td>
                            <td><?= htmlspecialchars($cust['email']) ?></td>
                            <td><?= htmlspecialchars($cust['phone']) ?></td>
                            <td>
                                <?php if ($customer_rating['total_ratings'] > 0): ?>
                                    <span class="text-warning">
                                        <?php
                                        $stars = round($customer_rating['avg_rating']);
                                        for ($i = 1; $i <= 5; $i++) {
                                            echo $i <= $stars ? '★' : '☆';
                                        }
                                        ?>
                                    </span>
                                    <small class="text-muted">(<?= number_format($customer_rating['avg_rating'], 1) ?>)</small>
                                <?php else: ?>
                                    <span class="text-muted">لا يوجد تقييم</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <a href="customer_profile.php?id=<?= $cust['id'] ?>" class="btn btn-sm btn-info">
                                    <i class="fas fa-user"></i> الملف الشخصي
                                </a>
                                <a href="edit.php?id=<?= $cust['id'] ?>" class="btn btn-sm btn-warning">
                                    <i class="fas fa-edit"></i> تعديل
                                </a>
                                <a href="actions/delete_customer.php?id=<?= $cust['id'] ?>"
                                   class="btn btn-sm btn-danger"
                                   onclick="return confirm('هل أنت متأكد؟')">
                                   <i class="fas fa-trash"></i> حذف
                                </a>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>