<?php
require_once 'models/Customer.php';

$customer = new Customer();
$customers = $customer->getAll();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة العملاء</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h1 class="text-center mb-4">إدارة العملاء</h1>
        
        <div class="row">
            <div class="col-md-4">
                <h3>إضافة عميل جديد</h3>
                <form action="actions/add_customer.php" method="POST">
                    <div class="mb-3">
                        <label class="form-label">الاسم</label>
                        <input type="text" class="form-control" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">البريد الإلكتروني</label>
                        <input type="email" class="form-control" name="email" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">الهاتف</label>
                        <input type="text" class="form-control" name="phone">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">العنوان</label>
                        <textarea class="form-control" name="address" rows="3"></textarea>
                    </div>
                    <button type="submit" class="btn btn-primary">إضافة العميل</button>
                </form>
            </div>
            
            <div class="col-md-8">
                <h3>قائمة العملاء</h3>
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>الرقم</th>
                            <th>الاسم</th>
                            <th>البريد الإلكتروني</th>
                            <th>الهاتف</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach($customers as $cust): ?>
                        <tr>
                            <td><?= $cust['id'] ?></td>
                            <td><?= htmlspecialchars($cust['name']) ?></td>
                            <td><?= htmlspecialchars($cust['email']) ?></td>
                            <td><?= htmlspecialchars($cust['phone']) ?></td>
                            <td>
                                <a href="edit.php?id=<?= $cust['id'] ?>" class="btn btn-sm btn-warning">تعديل</a>
                                <a href="actions/delete_customer.php?id=<?= $cust['id'] ?>" 
                                   class="btn btn-sm btn-danger" 
                                   onclick="return confirm('هل أنت متأكد؟')">حذف</a>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</body>
</html>