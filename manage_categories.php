<?php
require_once __DIR__ . '/models/RatingCategory.php';

$ratingCategory = new RatingCategory();
$categories = $ratingCategory->getAll();
$categoryStats = $ratingCategory->getCategoryStats();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة فئات التقييم</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .rating-stars { color: #ffc107; }
        .category-card { transition: transform 0.2s; }
        .category-card:hover { transform: translateY(-2px); }
        .inactive-category { opacity: 0.6; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <h1><i class="fas fa-tags"></i> إدارة فئات التقييم</h1>
                    <div>
                        <button class="btn btn-success me-2" data-bs-toggle="modal" data-bs-target="#addCategoryModal">
                            <i class="fas fa-plus"></i> إضافة فئة جديدة
                        </button>
                        <a href="dashboard.php" class="btn btn-primary me-2">
                            <i class="fas fa-chart-line"></i> لوحة التحكم
                        </a>
                        <a href="index.php" class="btn btn-secondary">
                            <i class="fas fa-users"></i> العملاء
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Success/Error Messages -->
        <?php if (isset($_GET['success'])): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle"></i>
                <?php
                switch($_GET['success']) {
                    case 'category_added': echo 'تم إضافة الفئة بنجاح!'; break;
                    case 'category_updated': echo 'تم تحديث الفئة بنجاح!'; break;
                    case 'category_deleted': echo 'تم حذف الفئة بنجاح!'; break;
                    case 'category_toggled': echo 'تم تغيير حالة الفئة بنجاح!'; break;
                    default: echo 'تم تنفيذ العملية بنجاح!';
                }
                ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if (isset($_GET['error'])): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-triangle"></i>
                <?php
                switch($_GET['error']) {
                    case 'name_exists': echo 'اسم الفئة موجود مسبقاً!'; break;
                    case 'delete_failed': echo 'فشل في حذف الفئة!'; break;
                    case 'invalid_data': echo 'البيانات المدخلة غير صحيحة!'; break;
                    default: echo 'حدث خطأ غير متوقع!';
                }
                ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- Categories Grid -->
        <div class="row">
            <?php foreach ($categories as $category): ?>
                <?php 
                $stats = null;
                foreach ($categoryStats as $stat) {
                    if ($stat['id'] == $category['id']) {
                        $stats = $stat;
                        break;
                    }
                }
                ?>
                <div class="col-md-6 col-lg-4 mb-4">
                    <div class="card category-card <?= !$category['is_active'] ? 'inactive-category' : '' ?>">
                        <div class="card-header">
                            <div class="d-flex justify-content-between align-items-center">
                                <h6 class="mb-0">
                                    <i class="fas fa-tag"></i> 
                                    <?= htmlspecialchars($category['name']) ?>
                                    <?php if (!$category['is_active']): ?>
                                        <span class="badge bg-secondary ms-2">غير نشط</span>
                                    <?php endif; ?>
                                </h6>
                                <div class="dropdown">
                                    <button class="btn btn-sm btn-outline-secondary" type="button" data-bs-toggle="dropdown">
                                        <i class="fas fa-ellipsis-v"></i>
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li>
                                            <a class="dropdown-item" href="#" onclick="editCategory(<?= $category['id'] ?>)">
                                                <i class="fas fa-edit"></i> تعديل
                                            </a>
                                        </li>
                                        <li>
                                            <a class="dropdown-item" href="actions/toggle_category.php?id=<?= $category['id'] ?>">
                                                <i class="fas fa-<?= $category['is_active'] ? 'eye-slash' : 'eye' ?>"></i>
                                                <?= $category['is_active'] ? 'إلغاء تفعيل' : 'تفعيل' ?>
                                            </a>
                                        </li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li>
                                            <a class="dropdown-item text-danger" href="#" onclick="deleteCategory(<?= $category['id'] ?>)">
                                                <i class="fas fa-trash"></i> حذف
                                            </a>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <?php if ($category['description']): ?>
                                <p class="card-text text-muted"><?= htmlspecialchars($category['description']) ?></p>
                            <?php endif; ?>
                            
                            <?php if ($stats && $stats['total_ratings'] > 0): ?>
                                <div class="mt-3">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <span>متوسط التقييم:</span>
                                        <div>
                                            <span class="rating-stars">
                                                <?php 
                                                $stars = round($stats['avg_rating']);
                                                for ($i = 1; $i <= 5; $i++) {
                                                    echo $i <= $stars ? '★' : '☆';
                                                }
                                                ?>
                                            </span>
                                            <span class="ms-2"><?= number_format($stats['avg_rating'], 1) ?></span>
                                        </div>
                                    </div>
                                    <small class="text-muted"><?= $stats['total_ratings'] ?> تقييم</small>
                                </div>
                            <?php else: ?>
                                <div class="mt-3">
                                    <small class="text-muted">لا توجد تقييمات بعد</small>
                                </div>
                            <?php endif; ?>
                        </div>
                        <div class="card-footer">
                            <small class="text-muted">
                                ترتيب: <?= $category['sort_order'] ?> | 
                                تم الإنشاء: <?= date('Y-m-d', strtotime($category['created_at'])) ?>
                            </small>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>

        <?php if (empty($categories)): ?>
            <div class="row">
                <div class="col-12 text-center">
                    <div class="card">
                        <div class="card-body">
                            <i class="fas fa-tags fa-3x text-muted mb-3"></i>
                            <h4>لا توجد فئات بعد</h4>
                            <p class="text-muted">ابدأ بإضافة فئات للتقييمات</p>
                            <button class="btn btn-success" data-bs-toggle="modal" data-bs-target="#addCategoryModal">
                                <i class="fas fa-plus"></i> إضافة أول فئة
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>

    <!-- Add Category Modal -->
    <div class="modal fade" id="addCategoryModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">إضافة فئة تقييم جديدة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form action="actions/add_category.php" method="POST">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label class="form-label">اسم الفئة</label>
                            <input type="text" class="form-control" name="name" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">الوصف</label>
                            <textarea class="form-control" name="description" rows="3"></textarea>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">ترتيب العرض</label>
                            <input type="number" class="form-control" name="sort_order" value="<?= count($categories) + 1 ?>" min="1">
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-success">إضافة الفئة</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function editCategory(categoryId) {
            alert('ميزة التعديل ستكون متاحة قريباً');
        }

        function deleteCategory(categoryId) {
            if (confirm('هل أنت متأكد من حذف هذه الفئة؟ سيتم حذف جميع التقييمات المرتبطة بها.')) {
                window.location.href = 'actions/delete_category.php?id=' + categoryId;
            }
        }

        // Auto-dismiss alerts
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(alert => {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 5000);
    </script>
</body>
</html>
