<?php
require_once __DIR__ . '/models/SurveyQuestion.php';
require_once __DIR__ . '/models/CustomerSurveyResponse.php';

$surveyQuestion = new SurveyQuestion();
$surveyResponse = new CustomerSurveyResponse();

$questions = $surveyQuestion->getAllActive();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة أسئلة الاستبيان</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .rating-stars { color: #ffc107; }
        .question-card { transition: transform 0.2s; }
        .question-card:hover { transform: translateY(-2px); }
    </style>
</head>
<body>
    <div class="container mt-4">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <h1><i class="fas fa-question-circle"></i> إدارة أسئلة الاستبيان</h1>
                    <div>
                        <button class="btn btn-success me-2" data-bs-toggle="modal" data-bs-target="#addQuestionModal">
                            <i class="fas fa-plus"></i> إضافة سؤال جديد
                        </button>
                        <a href="dashboard.php" class="btn btn-primary me-2">
                            <i class="fas fa-chart-line"></i> لوحة التحكم
                        </a>
                        <a href="index.php" class="btn btn-secondary">
                            <i class="fas fa-users"></i> العملاء
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Questions List -->
        <div class="row">
            <?php foreach ($questions as $index => $question): ?>
                <?php 
                $responses = $surveyResponse->getQuestionResponses($question['id']);
                $avg_rating = $surveyResponse->getAverageRating($question['id']);
                ?>
                <div class="col-md-6 mb-4">
                    <div class="card question-card">
                        <div class="card-header">
                            <div class="d-flex justify-content-between align-items-center">
                                <h6 class="mb-0">
                                    <span class="badge bg-primary me-2"><?= $index + 1 ?></span>
                                    <?= $question['question_type'] == 'rating' ? 'تقييم' : 'نص' ?>
                                </h6>
                                <div>
                                    <button class="btn btn-sm btn-warning" onclick="editQuestion(<?= $question['id'] ?>)">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-sm btn-danger" onclick="deleteQuestion(<?= $question['id'] ?>)">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <p class="card-text"><?= htmlspecialchars($question['question_text']) ?></p>
                            
                            <?php if ($question['question_type'] == 'rating' && $avg_rating['total_responses'] > 0): ?>
                                <div class="mt-3">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <span>متوسط التقييم:</span>
                                        <div>
                                            <span class="rating-stars">
                                                <?php 
                                                $stars = round($avg_rating['avg_rating']);
                                                for ($i = 1; $i <= 5; $i++) {
                                                    echo $i <= $stars ? '★' : '☆';
                                                }
                                                ?>
                                            </span>
                                            <span class="ms-2"><?= number_format($avg_rating['avg_rating'], 1) ?></span>
                                        </div>
                                    </div>
                                    <small class="text-muted"><?= $avg_rating['total_responses'] ?> إجابة</small>
                                </div>
                            <?php elseif ($question['question_type'] == 'text'): ?>
                                <div class="mt-3">
                                    <small class="text-muted"><?= count($responses) ?> إجابة نصية</small>
                                </div>
                            <?php else: ?>
                                <div class="mt-3">
                                    <small class="text-muted">لا توجد إجابات بعد</small>
                                </div>
                            <?php endif; ?>
                            
                            <div class="mt-2">
                                <button class="btn btn-sm btn-outline-primary" onclick="viewResponses(<?= $question['id'] ?>)">
                                    <i class="fas fa-eye"></i> عرض الإجابات
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>

        <?php if (empty($questions)): ?>
            <div class="row">
                <div class="col-12 text-center">
                    <div class="card">
                        <div class="card-body">
                            <i class="fas fa-question-circle fa-3x text-muted mb-3"></i>
                            <h4>لا توجد أسئلة بعد</h4>
                            <p class="text-muted">ابدأ بإضافة أسئلة للاستبيان</p>
                            <button class="btn btn-success" data-bs-toggle="modal" data-bs-target="#addQuestionModal">
                                <i class="fas fa-plus"></i> إضافة أول سؤال
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>

    <!-- Add Question Modal -->
    <div class="modal fade" id="addQuestionModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">إضافة سؤال جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form action="actions/add_question.php" method="POST">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label class="form-label">نص السؤال</label>
                            <textarea class="form-control" name="question_text" rows="3" required></textarea>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">نوع السؤال</label>
                            <select class="form-select" name="question_type" required>
                                <option value="rating">تقييم بالنجوم</option>
                                <option value="text">إجابة نصية</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">ترتيب السؤال</label>
                            <input type="number" class="form-control" name="sort_order" value="<?= count($questions) + 1 ?>" min="1">
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-success">إضافة السؤال</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function editQuestion(questionId) {
            // يمكن إضافة modal للتعديل لاحقاً
            alert('ميزة التعديل ستكون متاحة قريباً');
        }

        function deleteQuestion(questionId) {
            if (confirm('هل أنت متأكد من حذف هذا السؤال؟ سيتم حذف جميع الإجابات المرتبطة به.')) {
                window.location.href = 'actions/delete_question.php?id=' + questionId;
            }
        }

        function viewResponses(questionId) {
            window.open('question_responses.php?question_id=' + questionId, '_blank');
        }
    </script>
</body>
</html>
