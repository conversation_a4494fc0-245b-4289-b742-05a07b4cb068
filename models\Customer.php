<?php
require_once 'database/connection.php';

class Customer {
    private $pdo;
    
    public function __construct() {
        global $pdo;
        $this->pdo = $pdo;
    }
    
    public function create($name, $email, $phone, $address) {
        $sql = "INSERT INTO customers (name, email, phone, address) VALUES (?, ?, ?, ?)";
        $stmt = $this->pdo->prepare($sql);
        return $stmt->execute([$name, $email, $phone, $address]);
    }
    
    public function getAll() {
        $sql = "SELECT * FROM customers ORDER BY created_at DESC";
        $stmt = $this->pdo->query($sql);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    public function getById($id) {
        $sql = "SELECT * FROM customers WHERE id = ?";
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute([$id]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    public function update($id, $name, $email, $phone, $address) {
        $sql = "UPDATE customers SET name=?, email=?, phone=?, address=? WHERE id=?";
        $stmt = $this->pdo->prepare($sql);
        return $stmt->execute([$name, $email, $phone, $address, $id]);
    }
    
    public function delete($id) {
        $sql = "DELETE FROM customers WHERE id = ?";
        $stmt = $this->pdo->prepare($sql);
        return $stmt->execute([$id]);
    }
}
?>