<?php
require_once __DIR__ . '/../database/connection.php';

class CustomerFeedback {
    private $pdo;
    
    public function __construct() {
        global $pdo;
        $this->pdo = $pdo;
    }
    
    public function create($customer_id, $feedback_type, $title, $description, $priority = 'medium', $assigned_to = null) {
        $sql = "INSERT INTO customer_feedback (customer_id, feedback_type, title, description, priority, assigned_to) VALUES (?, ?, ?, ?, ?, ?)";
        $stmt = $this->pdo->prepare($sql);
        return $stmt->execute([$customer_id, $feedback_type, $title, $description, $priority, $assigned_to]);
    }
    
    public function getByCustomerId($customer_id) {
        $sql = "SELECT cf.*, c.name as customer_name 
                FROM customer_feedback cf 
                JOIN customers c ON cf.customer_id = c.id 
                WHERE cf.customer_id = ? 
                ORDER BY cf.created_at DESC";
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute([$customer_id]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    public function getAll() {
        $sql = "SELECT cf.*, c.name as customer_name 
                FROM customer_feedback cf 
                JOIN customers c ON cf.customer_id = c.id 
                ORDER BY cf.created_at DESC";
        $stmt = $this->pdo->query($sql);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    public function getByType($feedback_type) {
        $sql = "SELECT cf.*, c.name as customer_name 
                FROM customer_feedback cf 
                JOIN customers c ON cf.customer_id = c.id 
                WHERE cf.feedback_type = ? 
                ORDER BY cf.created_at DESC";
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute([$feedback_type]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    public function getByStatus($status) {
        $sql = "SELECT cf.*, c.name as customer_name 
                FROM customer_feedback cf 
                JOIN customers c ON cf.customer_id = c.id 
                WHERE cf.status = ? 
                ORDER BY cf.created_at DESC";
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute([$status]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    public function getByPriority($priority) {
        $sql = "SELECT cf.*, c.name as customer_name 
                FROM customer_feedback cf 
                JOIN customers c ON cf.customer_id = c.id 
                WHERE cf.priority = ? 
                ORDER BY cf.created_at DESC";
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute([$priority]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    public function getById($id) {
        $sql = "SELECT cf.*, c.name as customer_name 
                FROM customer_feedback cf 
                JOIN customers c ON cf.customer_id = c.id 
                WHERE cf.id = ?";
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute([$id]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    public function update($id, $feedback_type, $title, $description, $priority, $status, $assigned_to) {
        $sql = "UPDATE customer_feedback SET feedback_type=?, title=?, description=?, priority=?, status=?, assigned_to=?, updated_at=CURRENT_TIMESTAMP WHERE id=?";
        $stmt = $this->pdo->prepare($sql);
        return $stmt->execute([$feedback_type, $title, $description, $priority, $status, $assigned_to, $id]);
    }
    
    public function resolve($id, $resolution) {
        $sql = "UPDATE customer_feedback SET status='resolved', resolution=?, resolved_at=CURRENT_TIMESTAMP, updated_at=CURRENT_TIMESTAMP WHERE id=?";
        $stmt = $this->pdo->prepare($sql);
        return $stmt->execute([$resolution, $id]);
    }
    
    public function close($id) {
        $sql = "UPDATE customer_feedback SET status='closed', updated_at=CURRENT_TIMESTAMP WHERE id=?";
        $stmt = $this->pdo->prepare($sql);
        return $stmt->execute([$id]);
    }
    
    public function delete($id) {
        $sql = "DELETE FROM customer_feedback WHERE id = ?";
        $stmt = $this->pdo->prepare($sql);
        return $stmt->execute([$id]);
    }
    
    public function getFeedbackStats() {
        $sql = "SELECT 
                    feedback_type,
                    status,
                    priority,
                    COUNT(*) as count
                FROM customer_feedback 
                GROUP BY feedback_type, status, priority
                ORDER BY feedback_type, priority DESC";
        $stmt = $this->pdo->query($sql);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    public function getOpenFeedback() {
        $sql = "SELECT cf.*, c.name as customer_name 
                FROM customer_feedback cf 
                JOIN customers c ON cf.customer_id = c.id 
                WHERE cf.status IN ('open', 'in_progress') 
                ORDER BY 
                    CASE cf.priority 
                        WHEN 'urgent' THEN 1 
                        WHEN 'high' THEN 2 
                        WHEN 'medium' THEN 3 
                        WHEN 'low' THEN 4 
                    END,
                    cf.created_at ASC";
        $stmt = $this->pdo->query($sql);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
}
?>
