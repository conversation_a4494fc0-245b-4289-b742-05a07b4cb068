<?php
require_once __DIR__ . '/../database/connection.php';

class CustomerInteraction {
    private $pdo;
    
    public function __construct() {
        global $pdo;
        $this->pdo = $pdo;
    }
    
    public function create($customer_id, $interaction_type, $subject, $description = '', $duration_minutes = 0, $outcome = 'successful', $next_action = '', $interaction_date = null, $created_by = 'system') {
        if (!$interaction_date) {
            $interaction_date = date('Y-m-d H:i:s');
        }
        
        $sql = "INSERT INTO customer_interactions (customer_id, interaction_type, subject, description, duration_minutes, outcome, next_action, interaction_date, created_by) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
        $stmt = $this->pdo->prepare($sql);
        return $stmt->execute([$customer_id, $interaction_type, $subject, $description, $duration_minutes, $outcome, $next_action, $interaction_date, $created_by]);
    }
    
    public function getByCustomerId($customer_id) {
        $sql = "SELECT ci.*, c.name as customer_name 
                FROM customer_interactions ci 
                JOIN customers c ON ci.customer_id = c.id 
                WHERE ci.customer_id = ? 
                ORDER BY ci.interaction_date DESC";
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute([$customer_id]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    public function getAll() {
        $sql = "SELECT ci.*, c.name as customer_name 
                FROM customer_interactions ci 
                JOIN customers c ON ci.customer_id = c.id 
                ORDER BY ci.interaction_date DESC";
        $stmt = $this->pdo->query($sql);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    public function getByType($interaction_type) {
        $sql = "SELECT ci.*, c.name as customer_name 
                FROM customer_interactions ci 
                JOIN customers c ON ci.customer_id = c.id 
                WHERE ci.interaction_type = ? 
                ORDER BY ci.interaction_date DESC";
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute([$interaction_type]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    public function getByOutcome($outcome) {
        $sql = "SELECT ci.*, c.name as customer_name 
                FROM customer_interactions ci 
                JOIN customers c ON ci.customer_id = c.id 
                WHERE ci.outcome = ? 
                ORDER BY ci.interaction_date DESC";
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute([$outcome]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    public function getById($id) {
        $sql = "SELECT ci.*, c.name as customer_name 
                FROM customer_interactions ci 
                JOIN customers c ON ci.customer_id = c.id 
                WHERE ci.id = ?";
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute([$id]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    public function update($id, $interaction_type, $subject, $description, $duration_minutes, $outcome, $next_action, $interaction_date) {
        $sql = "UPDATE customer_interactions SET interaction_type=?, subject=?, description=?, duration_minutes=?, outcome=?, next_action=?, interaction_date=? WHERE id=?";
        $stmt = $this->pdo->prepare($sql);
        return $stmt->execute([$interaction_type, $subject, $description, $duration_minutes, $outcome, $next_action, $interaction_date, $id]);
    }
    
    public function delete($id) {
        $sql = "DELETE FROM customer_interactions WHERE id = ?";
        $stmt = $this->pdo->prepare($sql);
        return $stmt->execute([$id]);
    }
    
    public function getInteractionStats($customer_id = null) {
        if ($customer_id) {
            $sql = "SELECT 
                        interaction_type,
                        COUNT(*) as count,
                        AVG(duration_minutes) as avg_duration
                    FROM customer_interactions 
                    WHERE customer_id = ?
                    GROUP BY interaction_type";
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute([$customer_id]);
        } else {
            $sql = "SELECT 
                        interaction_type,
                        COUNT(*) as count,
                        AVG(duration_minutes) as avg_duration
                    FROM customer_interactions 
                    GROUP BY interaction_type";
            $stmt = $this->pdo->query($sql);
        }
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    public function getFollowUpNeeded() {
        $sql = "SELECT ci.*, c.name as customer_name 
                FROM customer_interactions ci 
                JOIN customers c ON ci.customer_id = c.id 
                WHERE ci.outcome = 'follow_up_needed' 
                ORDER BY ci.interaction_date ASC";
        $stmt = $this->pdo->query($sql);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
}
?>
