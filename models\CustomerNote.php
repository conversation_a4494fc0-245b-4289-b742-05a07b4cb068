<?php
require_once 'database/connection.php';

class CustomerNote {
    private $pdo;
    
    public function __construct() {
        global $pdo;
        $this->pdo = $pdo;
    }
    
    public function create($customer_id, $note, $note_type = 'general', $priority = 'medium', $created_by = 'system') {
        $sql = "INSERT INTO customer_notes (customer_id, note, note_type, priority, created_by) VALUES (?, ?, ?, ?, ?)";
        $stmt = $this->pdo->prepare($sql);
        return $stmt->execute([$customer_id, $note, $note_type, $priority, $created_by]);
    }
    
    public function getByCustomerId($customer_id) {
        $sql = "SELECT cn.*, c.name as customer_name 
                FROM customer_notes cn 
                JOIN customers c ON cn.customer_id = c.id 
                WHERE cn.customer_id = ? 
                ORDER BY cn.created_at DESC";
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute([$customer_id]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    public function getAll() {
        $sql = "SELECT cn.*, c.name as customer_name 
                FROM customer_notes cn 
                JOIN customers c ON cn.customer_id = c.id 
                ORDER BY cn.created_at DESC";
        $stmt = $this->pdo->query($sql);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    public function getByPriority($priority) {
        $sql = "SELECT cn.*, c.name as customer_name 
                FROM customer_notes cn 
                JOIN customers c ON cn.customer_id = c.id 
                WHERE cn.priority = ? 
                ORDER BY cn.created_at DESC";
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute([$priority]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    public function getByType($note_type) {
        $sql = "SELECT cn.*, c.name as customer_name 
                FROM customer_notes cn 
                JOIN customers c ON cn.customer_id = c.id 
                WHERE cn.note_type = ? 
                ORDER BY cn.created_at DESC";
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute([$note_type]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    public function getById($id) {
        $sql = "SELECT cn.*, c.name as customer_name 
                FROM customer_notes cn 
                JOIN customers c ON cn.customer_id = c.id 
                WHERE cn.id = ?";
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute([$id]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    public function update($id, $note, $note_type, $priority) {
        $sql = "UPDATE customer_notes SET note=?, note_type=?, priority=? WHERE id=?";
        $stmt = $this->pdo->prepare($sql);
        return $stmt->execute([$note, $note_type, $priority, $id]);
    }
    
    public function delete($id) {
        $sql = "DELETE FROM customer_notes WHERE id = ?";
        $stmt = $this->pdo->prepare($sql);
        return $stmt->execute([$id]);
    }
    
    public function getNotesCount($customer_id = null) {
        if ($customer_id) {
            $sql = "SELECT COUNT(*) as total FROM customer_notes WHERE customer_id = ?";
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute([$customer_id]);
        } else {
            $sql = "SELECT COUNT(*) as total FROM customer_notes";
            $stmt = $this->pdo->query($sql);
        }
        return $stmt->fetch(PDO::FETCH_ASSOC)['total'];
    }
}
?>
