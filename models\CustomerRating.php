<?php
require_once __DIR__ . '/../database/connection.php';

class CustomerRating {
    private $pdo;
    
    public function __construct() {
        global $pdo;
        $this->pdo = $pdo;
    }
    
    public function create($customer_id, $rating, $comment = '', $category_id = null) {
        $sql = "INSERT INTO customer_ratings (customer_id, rating, comment, category_id) VALUES (?, ?, ?, ?)";
        $stmt = $this->pdo->prepare($sql);
        return $stmt->execute([$customer_id, $rating, $comment, $category_id]);
    }
    
    public function getByCustomerId($customer_id) {
        $sql = "SELECT cr.*, c.name as customer_name, rc.name as category_name
                FROM customer_ratings cr
                JOIN customers c ON cr.customer_id = c.id
                LEFT JOIN rating_categories rc ON cr.category_id = rc.id
                WHERE cr.customer_id = ?
                ORDER BY cr.created_at DESC";
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute([$customer_id]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    public function getAll() {
        $sql = "SELECT cr.*, c.name as customer_name, rc.name as category_name
                FROM customer_ratings cr
                JOIN customers c ON cr.customer_id = c.id
                LEFT JOIN rating_categories rc ON cr.category_id = rc.id
                ORDER BY cr.created_at DESC";
        $stmt = $this->pdo->query($sql);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    public function getAverageRating($customer_id = null) {
        if ($customer_id) {
            $sql = "SELECT AVG(rating) as avg_rating, COUNT(*) as total_ratings 
                    FROM customer_ratings WHERE customer_id = ?";
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute([$customer_id]);
        } else {
            $sql = "SELECT AVG(rating) as avg_rating, COUNT(*) as total_ratings 
                    FROM customer_ratings";
            $stmt = $this->pdo->query($sql);
        }
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    public function getRatingsByCategory($category_id = null) {
        if ($category_id) {
            $sql = "SELECT cr.*, c.name as customer_name, rc.name as category_name
                    FROM customer_ratings cr
                    JOIN customers c ON cr.customer_id = c.id
                    LEFT JOIN rating_categories rc ON cr.category_id = rc.id
                    WHERE cr.category_id = ?
                    ORDER BY cr.created_at DESC";
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute([$category_id]);
        } else {
            $sql = "SELECT rc.id as category_id, rc.name as category_name, AVG(cr.rating) as avg_rating, COUNT(cr.id) as total_ratings
                    FROM rating_categories rc
                    LEFT JOIN customer_ratings cr ON rc.id = cr.category_id
                    WHERE rc.is_active = 1
                    GROUP BY rc.id
                    ORDER BY avg_rating DESC";
            $stmt = $this->pdo->query($sql);
        }
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    public function delete($id) {
        $sql = "DELETE FROM customer_ratings WHERE id = ?";
        $stmt = $this->pdo->prepare($sql);
        return $stmt->execute([$id]);
    }
    
    public function update($id, $rating, $comment, $category_id) {
        $sql = "UPDATE customer_ratings SET rating=?, comment=?, category_id=? WHERE id=?";
        $stmt = $this->pdo->prepare($sql);
        return $stmt->execute([$rating, $comment, $category_id, $id]);
    }
}
?>
