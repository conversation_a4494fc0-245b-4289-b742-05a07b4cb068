<?php
require_once __DIR__ . '/../database/connection.php';

class CustomerRating {
    private $pdo;
    
    public function __construct() {
        global $pdo;
        $this->pdo = $pdo;
    }
    
    public function create($customer_id, $rating, $comment = '', $category = 'general') {
        $sql = "INSERT INTO customer_ratings (customer_id, rating, comment, category) VALUES (?, ?, ?, ?)";
        $stmt = $this->pdo->prepare($sql);
        return $stmt->execute([$customer_id, $rating, $comment, $category]);
    }
    
    public function getByCustomerId($customer_id) {
        $sql = "SELECT cr.*, c.name as customer_name 
                FROM customer_ratings cr 
                JOIN customers c ON cr.customer_id = c.id 
                WHERE cr.customer_id = ? 
                ORDER BY cr.created_at DESC";
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute([$customer_id]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    public function getAll() {
        $sql = "SELECT cr.*, c.name as customer_name 
                FROM customer_ratings cr 
                JOIN customers c ON cr.customer_id = c.id 
                ORDER BY cr.created_at DESC";
        $stmt = $this->pdo->query($sql);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    public function getAverageRating($customer_id = null) {
        if ($customer_id) {
            $sql = "SELECT AVG(rating) as avg_rating, COUNT(*) as total_ratings 
                    FROM customer_ratings WHERE customer_id = ?";
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute([$customer_id]);
        } else {
            $sql = "SELECT AVG(rating) as avg_rating, COUNT(*) as total_ratings 
                    FROM customer_ratings";
            $stmt = $this->pdo->query($sql);
        }
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    public function getRatingsByCategory($category = null) {
        if ($category) {
            $sql = "SELECT cr.*, c.name as customer_name 
                    FROM customer_ratings cr 
                    JOIN customers c ON cr.customer_id = c.id 
                    WHERE cr.category = ? 
                    ORDER BY cr.created_at DESC";
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute([$category]);
        } else {
            $sql = "SELECT category, AVG(rating) as avg_rating, COUNT(*) as total_ratings 
                    FROM customer_ratings 
                    GROUP BY category 
                    ORDER BY avg_rating DESC";
            $stmt = $this->pdo->query($sql);
        }
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    public function delete($id) {
        $sql = "DELETE FROM customer_ratings WHERE id = ?";
        $stmt = $this->pdo->prepare($sql);
        return $stmt->execute([$id]);
    }
    
    public function update($id, $rating, $comment, $category) {
        $sql = "UPDATE customer_ratings SET rating=?, comment=?, category=? WHERE id=?";
        $stmt = $this->pdo->prepare($sql);
        return $stmt->execute([$rating, $comment, $category, $id]);
    }
}
?>
