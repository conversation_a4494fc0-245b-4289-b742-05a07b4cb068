<?php
require_once __DIR__ . '/../database/connection.php';

class CustomerSurveyResponse {
    private $pdo;
    
    public function __construct() {
        global $pdo;
        $this->pdo = $pdo;
    }
    
    public function saveResponse($customer_id, $question_id, $rating_value = null, $text_value = null) {
        // حذف الإجابة السابقة إن وجدت
        $this->deleteResponse($customer_id, $question_id);
        
        $sql = "INSERT INTO customer_survey_responses (customer_id, question_id, rating_value, text_value) VALUES (?, ?, ?, ?)";
        $stmt = $this->pdo->prepare($sql);
        return $stmt->execute([$customer_id, $question_id, $rating_value, $text_value]);
    }
    
    public function deleteResponse($customer_id, $question_id) {
        $sql = "DELETE FROM customer_survey_responses WHERE customer_id = ? AND question_id = ?";
        $stmt = $this->pdo->prepare($sql);
        return $stmt->execute([$customer_id, $question_id]);
    }
    
    public function getCustomerResponses($customer_id) {
        $sql = "SELECT csr.*, sq.question_text, sq.question_type 
                FROM customer_survey_responses csr 
                JOIN survey_questions sq ON csr.question_id = sq.id 
                WHERE csr.customer_id = ? 
                ORDER BY sq.sort_order ASC";
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute([$customer_id]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    public function getQuestionResponses($question_id) {
        $sql = "SELECT csr.*, c.name as customer_name 
                FROM customer_survey_responses csr 
                JOIN customers c ON csr.customer_id = c.id 
                WHERE csr.question_id = ? 
                ORDER BY csr.created_at DESC";
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute([$question_id]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    public function getAverageRating($question_id) {
        $sql = "SELECT AVG(rating_value) as avg_rating, COUNT(*) as total_responses 
                FROM customer_survey_responses 
                WHERE question_id = ? AND rating_value IS NOT NULL";
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute([$question_id]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    public function getAllResponses() {
        $sql = "SELECT csr.*, c.name as customer_name, sq.question_text, sq.question_type 
                FROM customer_survey_responses csr 
                JOIN customers c ON csr.customer_id = c.id 
                JOIN survey_questions sq ON csr.question_id = sq.id 
                ORDER BY csr.created_at DESC";
        $stmt = $this->pdo->query($sql);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    public function hasCustomerCompletedSurvey($customer_id) {
        $sql = "SELECT COUNT(DISTINCT question_id) as answered_questions,
                       (SELECT COUNT(*) FROM survey_questions WHERE is_active = 1) as total_questions
                FROM customer_survey_responses 
                WHERE customer_id = ?";
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute([$customer_id]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        return $result['answered_questions'] >= $result['total_questions'];
    }
    
    public function getCustomerOverallRating($customer_id) {
        $sql = "SELECT AVG(rating_value) as avg_rating, COUNT(*) as total_ratings 
                FROM customer_survey_responses 
                WHERE customer_id = ? AND rating_value IS NOT NULL";
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute([$customer_id]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
}
?>
