<?php
require_once __DIR__ . '/../database/connection.php';

class RatingCategory {
    private $pdo;
    
    public function __construct() {
        global $pdo;
        $this->pdo = $pdo;
    }
    
    public function create($name, $description = '', $sort_order = 0) {
        $sql = "INSERT INTO rating_categories (name, description, sort_order) VALUES (?, ?, ?)";
        $stmt = $this->pdo->prepare($sql);
        return $stmt->execute([$name, $description, $sort_order]);
    }
    
    public function getAll() {
        $sql = "SELECT * FROM rating_categories ORDER BY sort_order ASC, name ASC";
        $stmt = $this->pdo->query($sql);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    public function getAllActive() {
        $sql = "SELECT * FROM rating_categories WHERE is_active = 1 ORDER BY sort_order ASC, name ASC";
        $stmt = $this->pdo->query($sql);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    public function getById($id) {
        $sql = "SELECT * FROM rating_categories WHERE id = ?";
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute([$id]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    public function update($id, $name, $description, $sort_order, $is_active) {
        $sql = "UPDATE rating_categories SET name=?, description=?, sort_order=?, is_active=? WHERE id=?";
        $stmt = $this->pdo->prepare($sql);
        return $stmt->execute([$name, $description, $sort_order, $is_active, $id]);
    }
    
    public function delete($id) {
        $sql = "DELETE FROM rating_categories WHERE id = ?";
        $stmt = $this->pdo->prepare($sql);
        return $stmt->execute([$id]);
    }
    
    public function toggleActive($id) {
        $sql = "UPDATE rating_categories SET is_active = NOT is_active WHERE id = ?";
        $stmt = $this->pdo->prepare($sql);
        return $stmt->execute([$id]);
    }
    
    public function getCategoryStats($category_id = null) {
        if ($category_id) {
            $sql = "SELECT 
                        rc.name as category_name,
                        AVG(cr.rating) as avg_rating,
                        COUNT(cr.id) as total_ratings
                    FROM rating_categories rc
                    LEFT JOIN customer_ratings cr ON rc.id = cr.category_id
                    WHERE rc.id = ?
                    GROUP BY rc.id";
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute([$category_id]);
        } else {
            $sql = "SELECT 
                        rc.id,
                        rc.name as category_name,
                        AVG(cr.rating) as avg_rating,
                        COUNT(cr.id) as total_ratings
                    FROM rating_categories rc
                    LEFT JOIN customer_ratings cr ON rc.id = cr.category_id
                    WHERE rc.is_active = 1
                    GROUP BY rc.id
                    ORDER BY rc.sort_order ASC";
            $stmt = $this->pdo->query($sql);
        }
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    public function getTopRatedCategories($limit = 5) {
        $sql = "SELECT 
                    rc.name as category_name,
                    AVG(cr.rating) as avg_rating,
                    COUNT(cr.id) as total_ratings
                FROM rating_categories rc
                LEFT JOIN customer_ratings cr ON rc.id = cr.category_id
                WHERE rc.is_active = 1 AND cr.rating IS NOT NULL
                GROUP BY rc.id
                HAVING total_ratings > 0
                ORDER BY avg_rating DESC, total_ratings DESC
                LIMIT ?";
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute([$limit]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    public function nameExists($name, $exclude_id = null) {
        if ($exclude_id) {
            $sql = "SELECT COUNT(*) FROM rating_categories WHERE name = ? AND id != ?";
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute([$name, $exclude_id]);
        } else {
            $sql = "SELECT COUNT(*) FROM rating_categories WHERE name = ?";
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute([$name]);
        }
        return $stmt->fetchColumn() > 0;
    }
}
?>
