<?php
require_once __DIR__ . '/../database/connection.php';

class SurveyQuestion {
    private $pdo;
    
    public function __construct() {
        global $pdo;
        $this->pdo = $pdo;
    }
    
    public function getAllActive() {
        $sql = "SELECT * FROM survey_questions WHERE is_active = 1 ORDER BY sort_order ASC";
        $stmt = $this->pdo->query($sql);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    public function getById($id) {
        $sql = "SELECT * FROM survey_questions WHERE id = ?";
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute([$id]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    public function create($question_text, $question_type = 'rating', $sort_order = 0) {
        $sql = "INSERT INTO survey_questions (question_text, question_type, sort_order) VALUES (?, ?, ?)";
        $stmt = $this->pdo->prepare($sql);
        return $stmt->execute([$question_text, $question_type, $sort_order]);
    }
    
    public function update($id, $question_text, $question_type, $sort_order, $is_active) {
        $sql = "UPDATE survey_questions SET question_text=?, question_type=?, sort_order=?, is_active=? WHERE id=?";
        $stmt = $this->pdo->prepare($sql);
        return $stmt->execute([$question_text, $question_type, $sort_order, $is_active, $id]);
    }
    
    public function delete($id) {
        $sql = "DELETE FROM survey_questions WHERE id = ?";
        $stmt = $this->pdo->prepare($sql);
        return $stmt->execute([$id]);
    }
}
?>
