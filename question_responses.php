<?php
require_once __DIR__ . '/models/SurveyQuestion.php';
require_once __DIR__ . '/models/CustomerSurveyResponse.php';

if (!isset($_GET['question_id'])) {
    header('Location: manage_questions.php?error=missing_question_id');
    exit;
}

$question_id = $_GET['question_id'];
$surveyQuestion = new SurveyQuestion();
$surveyResponse = new CustomerSurveyResponse();

$question = $surveyQuestion->getById($question_id);
if (!$question) {
    header('Location: manage_questions.php?error=question_not_found');
    exit;
}

$responses = $surveyResponse->getQuestionResponses($question_id);
$avg_rating = $surveyResponse->getAverageRating($question_id);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إجابات السؤال</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .rating-stars { color: #ffc107; }
        .response-card { transition: transform 0.2s; }
        .response-card:hover { transform: translateY(-2px); }
    </style>
</head>
<body>
    <div class="container mt-4">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <h1><i class="fas fa-comments"></i> إجابات السؤال</h1>
                    <button onclick="window.close()" class="btn btn-secondary">
                        <i class="fas fa-times"></i> إغلاق
                    </button>
                </div>
            </div>
        </div>

        <!-- Question Info -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-question-circle"></i> 
                            <?= htmlspecialchars($question['question_text']) ?>
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4 text-center">
                                <h4 class="text-primary"><?= count($responses) ?></h4>
                                <p class="mb-0">إجمالي الإجابات</p>
                            </div>
                            <div class="col-md-4 text-center">
                                <h4 class="text-info">
                                    <?= $question['question_type'] == 'rating' ? 'تقييم' : 'نص' ?>
                                </h4>
                                <p class="mb-0">نوع السؤال</p>
                            </div>
                            <div class="col-md-4 text-center">
                                <?php if ($question['question_type'] == 'rating' && $avg_rating['total_responses'] > 0): ?>
                                    <h4 class="text-warning">
                                        <span class="rating-stars">
                                            <?php 
                                            $stars = round($avg_rating['avg_rating']);
                                            for ($i = 1; $i <= 5; $i++) {
                                                echo $i <= $stars ? '★' : '☆';
                                            }
                                            ?>
                                        </span>
                                    </h4>
                                    <p class="mb-0">متوسط التقييم: <?= number_format($avg_rating['avg_rating'], 1) ?></p>
                                <?php else: ?>
                                    <h4 class="text-muted">-</h4>
                                    <p class="mb-0">لا يوجد تقييم</p>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Responses -->
        <div class="row">
            <div class="col-12">
                <?php if (empty($responses)): ?>
                    <div class="card">
                        <div class="card-body text-center">
                            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                            <h4>لا توجد إجابات بعد</h4>
                            <p class="text-muted">لم يجب أي عميل على هذا السؤال حتى الآن</p>
                        </div>
                    </div>
                <?php else: ?>
                    <?php foreach ($responses as $response): ?>
                        <div class="card response-card mb-3">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div>
                                        <h6 class="card-title">
                                            <i class="fas fa-user"></i> 
                                            <?= htmlspecialchars($response['customer_name']) ?>
                                        </h6>
                                        
                                        <?php if ($question['question_type'] == 'rating' && $response['rating_value']): ?>
                                            <div class="rating-stars mb-2">
                                                <?php for ($i = 1; $i <= 5; $i++): ?>
                                                    <?= $i <= $response['rating_value'] ? '★' : '☆' ?>
                                                <?php endfor; ?>
                                                <span class="ms-2 text-muted">(<?= $response['rating_value'] ?>/5)</span>
                                            </div>
                                        <?php endif; ?>
                                        
                                        <?php if ($response['text_value']): ?>
                                            <p class="card-text"><?= nl2br(htmlspecialchars($response['text_value'])) ?></p>
                                        <?php endif; ?>
                                    </div>
                                    <div class="text-end">
                                        <small class="text-muted">
                                            <i class="fas fa-clock"></i>
                                            <?= date('Y-m-d H:i', strtotime($response['created_at'])) ?>
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>

        <!-- Statistics for Rating Questions -->
        <?php if ($question['question_type'] == 'rating' && !empty($responses)): ?>
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h6><i class="fas fa-chart-bar"></i> توزيع التقييمات</h6>
                        </div>
                        <div class="card-body">
                            <?php
                            $rating_counts = [1 => 0, 2 => 0, 3 => 0, 4 => 0, 5 => 0];
                            foreach ($responses as $response) {
                                if ($response['rating_value']) {
                                    $rating_counts[$response['rating_value']]++;
                                }
                            }
                            $total_ratings = array_sum($rating_counts);
                            ?>
                            
                            <?php for ($i = 5; $i >= 1; $i--): ?>
                                <div class="row mb-2 align-items-center">
                                    <div class="col-2">
                                        <span class="rating-stars">
                                            <?php for ($j = 1; $j <= 5; $j++): ?>
                                                <?= $j <= $i ? '★' : '☆' ?>
                                            <?php endfor; ?>
                                        </span>
                                    </div>
                                    <div class="col-8">
                                        <div class="progress">
                                            <div class="progress-bar bg-warning" 
                                                 style="width: <?= $total_ratings > 0 ? ($rating_counts[$i] / $total_ratings) * 100 : 0 ?>%">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-2 text-end">
                                        <span class="badge bg-secondary"><?= $rating_counts[$i] ?></span>
                                    </div>
                                </div>
                            <?php endfor; ?>
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
