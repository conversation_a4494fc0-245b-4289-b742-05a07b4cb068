<?php
require_once 'models/Customer.php';
require_once 'models/CustomerRating.php';
require_once 'models/CustomerNote.php';
require_once 'models/CustomerInteraction.php';
require_once 'models/CustomerFeedback.php';

$customer = new Customer();
$rating = new CustomerRating();
$note = new CustomerNote();
$interaction = new CustomerInteraction();
$feedback = new CustomerFeedback();

// Get comprehensive statistics
$customers = $customer->getAll();
$all_ratings = $rating->getAll();
$rating_categories = $rating->getRatingsByCategory();
$interaction_stats = $interaction->getInteractionStats();
$feedback_stats = $feedback->getFeedbackStats();
$open_feedback = $feedback->getOpenFeedback();
$resolved_feedback = $feedback->getByStatus('resolved');

// Calculate additional metrics
$total_customers = count($customers);
$avg_rating_overall = $rating->getAverageRating();
$high_priority_feedback = $feedback->getByPriority('high');
$urgent_feedback = $feedback->getByPriority('urgent');
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقارير تجربة العملاء</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .rating-stars { color: #ffc107; }
        .report-card { transition: transform 0.2s; }
        .report-card:hover { transform: translateY(-2px); }
        .priority-urgent { border-left: 4px solid #6f42c1; }
        .priority-high { border-left: 4px solid #dc3545; }
        .priority-medium { border-left: 4px solid #ffc107; }
        .priority-low { border-left: 4px solid #28a745; }
        @media print {
            .no-print { display: none !important; }
            .container-fluid { margin: 0; padding: 0; }
        }
    </style>
</head>
<body>
    <div class="container-fluid mt-4">
        <!-- Header -->
        <div class="row mb-4 no-print">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <h1><i class="fas fa-file-alt"></i> تقارير تجربة العملاء</h1>
                    <div>
                        <button onclick="window.print()" class="btn btn-secondary me-2">
                            <i class="fas fa-print"></i> طباعة
                        </button>
                        <a href="dashboard.php" class="btn btn-primary me-2">
                            <i class="fas fa-chart-line"></i> لوحة التحكم
                        </a>
                        <a href="index.php" class="btn btn-info">
                            <i class="fas fa-users"></i> إدارة العملاء
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Executive Summary -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card report-card">
                    <div class="card-header bg-primary text-white">
                        <h5><i class="fas fa-chart-bar"></i> الملخص التنفيذي</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3 text-center">
                                <h3 class="text-primary"><?= $total_customers ?></h3>
                                <p class="mb-0">إجمالي العملاء</p>
                            </div>
                            <div class="col-md-3 text-center">
                                <h3 class="text-warning"><?= $avg_rating_overall['avg_rating'] ? number_format($avg_rating_overall['avg_rating'], 1) : '0.0' ?></h3>
                                <p class="mb-0">متوسط التقييم العام</p>
                            </div>
                            <div class="col-md-3 text-center">
                                <h3 class="text-info"><?= count($open_feedback) ?></h3>
                                <p class="mb-0">الشكاوى المفتوحة</p>
                            </div>
                            <div class="col-md-3 text-center">
                                <h3 class="text-success"><?= count($resolved_feedback) ?></h3>
                                <p class="mb-0">الشكاوى المحلولة</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Rating Analysis -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card report-card">
                    <div class="card-header bg-warning text-dark">
                        <h6><i class="fas fa-star"></i> تحليل التقييمات</h6>
                    </div>
                    <div class="card-body">
                        <h6>التقييمات حسب الفئة:</h6>
                        <?php if (!empty($rating_categories)): ?>
                            <?php foreach ($rating_categories as $category): ?>
                                <div class="d-flex justify-content-between mb-2">
                                    <span><?= htmlspecialchars($category['category']) ?></span>
                                    <span>
                                        <span class="rating-stars">
                                            <?php 
                                            $stars = round($category['avg_rating']);
                                            for ($i = 1; $i <= 5; $i++) {
                                                echo $i <= $stars ? '★' : '☆';
                                            }
                                            ?>
                                        </span>
                                        (<?= number_format($category['avg_rating'], 1) ?>)
                                    </span>
                                </div>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <p class="text-muted">لا توجد تقييمات بعد</p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card report-card">
                    <div class="card-header bg-info text-white">
                        <h6><i class="fas fa-comments"></i> إحصائيات التفاعلات</h6>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($interaction_stats)): ?>
                            <?php foreach ($interaction_stats as $stat): ?>
                                <div class="d-flex justify-content-between mb-2">
                                    <span>
                                        <?= $stat['interaction_type'] == 'call' ? 'مكالمات' : 
                                            ($stat['interaction_type'] == 'meeting' ? 'اجتماعات' : 
                                            ($stat['interaction_type'] == 'email' ? 'إيميلات' : 
                                            ($stat['interaction_type'] == 'chat' ? 'محادثات' : 'زيارات'))) ?>
                                    </span>
                                    <span>
                                        <strong><?= $stat['count'] ?></strong> تفاعل
                                        <?php if ($stat['avg_duration'] > 0): ?>
                                            <small class="text-muted">(متوسط: <?= round($stat['avg_duration']) ?> دقيقة)</small>
                                        <?php endif; ?>
                                    </span>
                                </div>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <p class="text-muted">لا توجد تفاعلات بعد</p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Feedback Analysis -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card report-card">
                    <div class="card-header bg-danger text-white">
                        <h6><i class="fas fa-exclamation-triangle"></i> تحليل الشكاوى والاقتراحات</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>حسب النوع:</h6>
                                <?php 
                                $feedback_by_type = [];
                                foreach ($feedback_stats as $stat) {
                                    if (!isset($feedback_by_type[$stat['feedback_type']])) {
                                        $feedback_by_type[$stat['feedback_type']] = 0;
                                    }
                                    $feedback_by_type[$stat['feedback_type']] += $stat['count'];
                                }
                                ?>
                                <?php foreach ($feedback_by_type as $type => $count): ?>
                                    <div class="d-flex justify-content-between mb-2">
                                        <span>
                                            <?= $type == 'complaint' ? 'شكاوى' : 
                                                ($type == 'suggestion' ? 'اقتراحات' : 
                                                ($type == 'compliment' ? 'إطراءات' : 'استفسارات')) ?>
                                        </span>
                                        <span><strong><?= $count ?></strong></span>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                            <div class="col-md-6">
                                <h6>حسب الأولوية:</h6>
                                <div class="d-flex justify-content-between mb-2">
                                    <span class="text-dark">عاجل</span>
                                    <span><strong><?= count($urgent_feedback) ?></strong></span>
                                </div>
                                <div class="d-flex justify-content-between mb-2">
                                    <span class="text-danger">عالي</span>
                                    <span><strong><?= count($high_priority_feedback) ?></strong></span>
                                </div>
                                <div class="d-flex justify-content-between mb-2">
                                    <span class="text-warning">متوسط</span>
                                    <span><strong><?= count($feedback->getByPriority('medium')) ?></strong></span>
                                </div>
                                <div class="d-flex justify-content-between mb-2">
                                    <span class="text-success">منخفض</span>
                                    <span><strong><?= count($feedback->getByPriority('low')) ?></strong></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div class="row">
            <div class="col-12 text-center">
                <small class="text-muted">
                    تم إنشاء هذا التقرير في <?= date('Y-m-d H:i:s') ?> |
                    نظام إدارة تجربة العملاء
                </small>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
