<?php
// ملف إعداد جدول الفئات
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once __DIR__ . '/database/connection.php';

echo "<h2>إعداد جدول فئات التقييم</h2>";

try {
    // التحقق من وجود الجدول
    $stmt = $pdo->query("SHOW TABLES LIKE 'rating_categories'");
    $tableExists = $stmt->rowCount() > 0;
    
    if (!$tableExists) {
        echo "<p style='color: orange;'>⚠️ جدول rating_categories غير موجود. سيتم إنشاؤه الآن...</p>";
        
        // إنشاء جدول فئات التقييم
        $sql = "CREATE TABLE rating_categories (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(100) NOT NULL UNIQUE,
            description TEXT,
            is_active BOOLEAN DEFAULT TRUE,
            sort_order INT DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )";
        
        $pdo->exec($sql);
        echo "<p style='color: green;'>✅ تم إنشاء جدول rating_categories بنجاح!</p>";
        
        // إدراج فئات افتراضية
        $categories = [
            ['عام', 'تقييم عام للخدمة', 1],
            ['جودة الخدمة', 'تقييم جودة الخدمة المقدمة', 2],
            ['سرعة الاستجابة', 'تقييم سرعة الرد والاستجابة', 3],
            ['الدعم الفني', 'تقييم مستوى الدعم الفني', 4],
            ['سهولة الاستخدام', 'تقييم سهولة استخدام الخدمة', 5],
            ['القيمة مقابل المال', 'تقييم القيمة مقابل السعر المدفوع', 6]
        ];
        
        $stmt = $pdo->prepare("INSERT INTO rating_categories (name, description, sort_order) VALUES (?, ?, ?)");
        
        foreach ($categories as $category) {
            $stmt->execute($category);
        }
        
        echo "<p style='color: green;'>✅ تم إدراج الفئات الافتراضية بنجاح!</p>";
        
    } else {
        echo "<p style='color: green;'>✅ جدول rating_categories موجود بالفعل!</p>";
    }
    
    // التحقق من وجود عمود category_id في جدول customer_ratings
    $stmt = $pdo->query("SHOW COLUMNS FROM customer_ratings LIKE 'category_id'");
    $columnExists = $stmt->rowCount() > 0;
    
    if (!$columnExists) {
        echo "<p style='color: orange;'>⚠️ عمود category_id غير موجود في جدول customer_ratings. سيتم إضافته الآن...</p>";
        
        // إضافة عمود category_id
        $pdo->exec("ALTER TABLE customer_ratings ADD COLUMN category_id INT DEFAULT NULL");
        
        // إضافة المفتاح الخارجي
        $pdo->exec("ALTER TABLE customer_ratings ADD FOREIGN KEY (category_id) REFERENCES rating_categories(id) ON DELETE SET NULL");
        
        echo "<p style='color: green;'>✅ تم إضافة عمود category_id وربطه بجدول الفئات!</p>";
        
        // حذف العمود القديم category إذا كان موجوداً
        $stmt = $pdo->query("SHOW COLUMNS FROM customer_ratings LIKE 'category'");
        $oldColumnExists = $stmt->rowCount() > 0;
        
        if ($oldColumnExists) {
            $pdo->exec("ALTER TABLE customer_ratings DROP COLUMN category");
            echo "<p style='color: green;'>✅ تم حذف العمود القديم category!</p>";
        }
        
    } else {
        echo "<p style='color: green;'>✅ عمود category_id موجود بالفعل في جدول customer_ratings!</p>";
    }
    
    // عرض الفئات الموجودة
    $stmt = $pdo->query("SELECT * FROM rating_categories ORDER BY sort_order ASC");
    $categories = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h3>الفئات الموجودة:</h3>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>ID</th><th>الاسم</th><th>الوصف</th><th>نشط</th><th>الترتيب</th></tr>";
    
    foreach ($categories as $category) {
        echo "<tr>";
        echo "<td>" . $category['id'] . "</td>";
        echo "<td>" . htmlspecialchars($category['name']) . "</td>";
        echo "<td>" . htmlspecialchars($category['description']) . "</td>";
        echo "<td>" . ($category['is_active'] ? 'نعم' : 'لا') . "</td>";
        echo "<td>" . $category['sort_order'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<hr>";
    echo "<p><strong>الآن يمكنك الذهاب إلى:</strong></p>";
    echo "<p><a href='manage_categories.php' style='color: blue;'>صفحة إدارة الفئات</a></p>";
    echo "<p><a href='index.php' style='color: blue;'>الصفحة الرئيسية</a></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ: " . $e->getMessage() . "</p>";
    echo "<p>تأكد من:</p>";
    echo "<ul>";
    echo "<li>تشغيل MySQL في XAMPP</li>";
    echo "<li>وجود قاعدة البيانات customer_db</li>";
    echo "<li>صحة إعدادات الاتصال</li>";
    echo "</ul>";
}
?>
