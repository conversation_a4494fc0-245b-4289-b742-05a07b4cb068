<?php
// ملف اختبار الاتصال بقاعدة البيانات
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>اختبار الاتصال بقاعدة البيانات</h2>";

try {
    require_once __DIR__ . '/database/connection.php';
    echo "<p style='color: green;'>✅ تم الاتصال بقاعدة البيانات بنجاح!</p>";
    
    // اختبار الجداول
    $tables = ['customers', 'customer_ratings', 'customer_notes', 'customer_interactions', 'customer_feedback', 'survey_questions', 'customer_survey_responses'];
    
    echo "<h3>اختبار الجداول:</h3>";
    foreach ($tables as $table) {
        try {
            $stmt = $pdo->query("SELECT COUNT(*) FROM $table");
            $count = $stmt->fetchColumn();
            echo "<p style='color: green;'>✅ جدول $table موجود ويحتوي على $count سجل</p>";
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ خطأ في جدول $table: " . $e->getMessage() . "</p>";
        }
    }
    
    echo "<h3>اختبار النماذج:</h3>";
    
    // اختبار نموذج العملاء
    try {
        require_once __DIR__ . '/models/Customer.php';
        $customer = new Customer();
        echo "<p style='color: green;'>✅ نموذج Customer يعمل بشكل صحيح</p>";
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ خطأ في نموذج Customer: " . $e->getMessage() . "</p>";
    }
    
    // اختبار نموذج التقييمات
    try {
        require_once __DIR__ . '/models/CustomerRating.php';
        $rating = new CustomerRating();
        echo "<p style='color: green;'>✅ نموذج CustomerRating يعمل بشكل صحيح</p>";
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ خطأ في نموذج CustomerRating: " . $e->getMessage() . "</p>";
    }

    // اختبار نموذج الأسئلة
    try {
        require_once __DIR__ . '/models/SurveyQuestion.php';
        $surveyQuestion = new SurveyQuestion();
        echo "<p style='color: green;'>✅ نموذج SurveyQuestion يعمل بشكل صحيح</p>";
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ خطأ في نموذج SurveyQuestion: " . $e->getMessage() . "</p>";
    }

    // اختبار نموذج إجابات الاستبيان
    try {
        require_once __DIR__ . '/models/CustomerSurveyResponse.php';
        $surveyResponse = new CustomerSurveyResponse();
        echo "<p style='color: green;'>✅ نموذج CustomerSurveyResponse يعمل بشكل صحيح</p>";
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ خطأ في نموذج CustomerSurveyResponse: " . $e->getMessage() . "</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ فشل الاتصال بقاعدة البيانات: " . $e->getMessage() . "</p>";
    echo "<p>تأكد من:</p>";
    echo "<ul>";
    echo "<li>تشغيل MySQL في XAMPP</li>";
    echo "<li>إنشاء قاعدة البيانات customer_db</li>";
    echo "<li>تشغيل ملف schema.sql</li>";
    echo "<li>صحة إعدادات database/connection.php</li>";
    echo "</ul>";
}

echo "<hr>";
echo "<p><a href='index.php'>العودة للصفحة الرئيسية</a></p>";
?>
